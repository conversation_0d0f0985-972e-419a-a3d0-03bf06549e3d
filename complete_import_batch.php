<?php
/**
 * Script per completare l'importazione in batch più piccoli
 */

require_once 'includes/db_config.php';
require_once 'includes/SimpleXLSX.php';

use <PERSON><PERSON><PERSON>\SimpleXLSX;

echo "=== COMPLETAMENTO IMPORTAZIONE IN BATCH ===\n\n";

$excel_file = 'docs/analisi_normative/conversione.xlsx';
$batch_size = 500; // Processa 500 righe alla volta
$start_row = 2002; // Inizia da dove si era fermato

try {
    echo "1. Apertura file Excel...\n";
    $xlsx = SimpleXLSX::parse($excel_file);
    
    if (!$xlsx) {
        die("ERRORE: " . SimpleXLSX::parseError() . "\n");
    }
    
    echo "   ✓ File aperto\n\n";
    
    echo "2. Connessione database...\n";
    $conn = new PDO("mysql:host=localhost;dbname=asdp_db;charset=utf8", "root", "");
    $conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "   ✓ Connesso al database\n\n";
    
    // Mappatura colonne
    $coordinate_columns = ['lat' => 2, 'lng' => 1];
    $tr_columns = [
        30   => ['ag' => 3,  'fo' => 4,  'tc' => 5],
        50   => ['ag' => 6,  'fo' => 7,  'tc' => 8],
        72   => ['ag' => 9,  'fo' => 10, 'tc' => 11],
        101  => ['ag' => 12, 'fo' => 13, 'tc' => 14],
        140  => ['ag' => 15, 'fo' => 16, 'tc' => 17],
        201  => ['ag' => 18, 'fo' => 19, 'tc' => 20],
        475  => ['ag' => 21, 'fo' => 22, 'tc' => 23],
        975  => ['ag' => 24, 'fo' => 25, 'tc' => 27],
        2475 => ['ag' => 28, 'fo' => 29, 'tc' => 30]
    ];
    
    // Prepara statement
    $update_statements = [];
    foreach ($tr_columns as $tr => $columns) {
        $update_statements[$tr] = $conn->prepare("
            UPDATE seismic_grid_points 
            SET ag_$tr = ?, fo_$tr = ?, tc_$tr = ?
            WHERE ABS(latitude - ?) < 0.001 AND ABS(longitude - ?) < 0.001
        ");
    }
    
    echo "3. Importazione in batch da riga $start_row...\n";
    
    $rows = $xlsx->rows(0);
    $current_row = 0;
    $total_processed = 0;
    $total_updated = 0;
    $batch_count = 0;
    
    foreach ($rows as $row) {
        $current_row++;
        
        // Salta fino alla riga di inizio
        if ($current_row <= $start_row) {
            continue;
        }
        
        $total_processed++;
        
        // Verifica che la riga abbia abbastanza colonne
        if (count($row) < 31) {
            continue;
        }
        
        // Estrai coordinate
        $lat = floatval($row[$coordinate_columns['lat']]);
        $lng = floatval($row[$coordinate_columns['lng']]);
        
        // Verifica validità coordinate Italia
        if ($lat < 35 || $lat > 48 || $lng < 6 || $lng > 19) {
            continue;
        }
        
        // Aggiorna i dati per ogni TR
        $row_updated = false;
        
        foreach ($tr_columns as $tr => $columns) {
            $ag = floatval($row[$columns['ag']]);
            $fo = floatval($row[$columns['fo']]);
            $tc = floatval($row[$columns['tc']]);
            
            if ($ag > 0 && $fo > 0 && $tc > 0) {
                if ($ag < 1.0) {
                    $ag = $ag * 9.81;
                }
                
                $update_statements[$tr]->execute([$ag, $fo, $tc, $lat, $lng]);
                if ($update_statements[$tr]->rowCount() > 0) {
                    $row_updated = true;
                }
            }
        }
        
        if ($row_updated) {
            $total_updated++;
        }
        
        // Ogni batch_size righe, mostra progresso
        if ($total_processed % $batch_size == 0) {
            $batch_count++;
            echo "   Batch $batch_count: processate $total_processed righe, aggiornate $total_updated\n";
            
            // Test rapido per vedere se abbiamo coperto la zona di test
            if ($batch_count % 4 == 0) { // Ogni 4 batch (2000 righe)
                echo "     Test rapido zona di interesse...\n";
                
                require_once 'includes/SeismicCalculator.php';
                $calculator = new SeismicCalculator();
                
                $result = $calculator->interpolateForTR(42.417406, 14.165970, 1462);
                $ag_error = abs($result['ag'] - 0.232) / 0.232 * 100;
                $tc_error = abs($result['TC*'] - 0.362) / 0.362 * 100;
                
                echo "     SLC: ag errore=" . number_format($ag_error, 1) . "%, tc errore=" . number_format($tc_error, 1) . "%\n";
                
                if ($ag_error < 10 && $tc_error < 10) {
                    echo "     ✓ Zona di test coperta! Errori accettabili.\n";
                    break;
                }
            }
        }
        
        // Limita a 5000 righe per evitare timeout
        if ($total_processed >= 5000) {
            echo "   Limite di 5000 righe raggiunto per questo batch.\n";
            break;
        }
    }
    
    echo "\n4. Risultati batch:\n";
    echo "   Righe processate: $total_processed\n";
    echo "   Righe aggiornate: $total_updated\n\n";
    
    // Test finale
    echo "5. Test finale...\n";
    
    require_once 'includes/SeismicCalculator.php';
    $calculator = new SeismicCalculator();
    
    $lat = 42.417406;
    $lng = 14.165970;
    
    $reference_values = [
        'SLV' => ['TR' => 712, 'ag' => 0.180, 'F0' => 2.481, 'TC' => 0.358],
        'SLC' => ['TR' => 1462, 'ag' => 0.232, 'F0' => 2.504, 'TC' => 0.362]
    ];
    
    foreach ($reference_values as $state => $ref) {
        echo "   $state (TR={$ref['TR']}):\n";
        $result = $calculator->interpolateForTR($lat, $lng, $ref['TR']);
        
        foreach (['ag', 'F0', 'TC'] as $param) {
            $param_key = ($param == 'TC') ? 'TC*' : $param;
            $expected = $ref[$param];
            $calculated = $result[$param_key];
            $error = abs($calculated - $expected) / $expected * 100;
            
            $status = $error < 10 ? "✓" : "✗";
            echo "     $param: " . number_format($calculated, 4) . " (atteso: " . number_format($expected, 4) . ", errore: " . number_format($error, 1) . "%) $status\n";
        }
        echo "\n";
    }
    
    echo "=== BATCH COMPLETATO ===\n";
    
} catch (Exception $e) {
    echo "ERRORE: " . $e->getMessage() . "\n";
}
?>
