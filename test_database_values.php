<?php
/**
 * Test per verificare i valori nel database
 * Controlla se i dati di riferimento sono corretti
 */

// Dati di test
$lat = 42.417406;
$lng = 14.165970;

echo "<h2>Verifica Valori Database</h2>\n";
echo "<p>Coordinate: Lat=$lat, Lng=$lng</p>\n";

try {
    $conn = new PDO("mysql:host=localhost;dbname=asdp_db;charset=utf8", "root", "");
    $conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // Trova i punti più vicini
    $stmt = $conn->prepare("
        SELECT 
            latitude, longitude,
            ag_30, ag_50, ag_72, ag_101, ag_140, ag_201, ag_475, ag_975, ag_2475,
            fo_30, fo_50, fo_72, fo_101, fo_140, fo_201, fo_475, fo_975, fo_2475,
            tc_30, tc_50, tc_72, tc_101, tc_140, tc_201, tc_475, tc_975, tc_2475,
            POW(latitude - ?, 2) + POW(longitude - ?, 2) as distance
        FROM seismic_grid_points
        WHERE latitude BETWEEN ? - 0.5 AND ? + 0.5 
        AND longitude BETWEEN ? - 0.5 AND ? + 0.5 
        ORDER BY POW(latitude - ?, 2) + POW(longitude - ?, 2)
        LIMIT 4
    ");
    
    $stmt->execute([$lat, $lng, $lat, $lat, $lng, $lng, $lat, $lng]);
    $points = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<h3>Punti più vicini nel database</h3>\n";
    foreach ($points as $i => $point) {
        echo "<h4>Punto " . ($i+1) . " - Lat: " . number_format($point['latitude'], 6) . ", Lng: " . number_format($point['longitude'], 6) . " (Distanza: " . number_format($point['distance'], 8) . ")</h4>\n";
        
        echo "<table border='1' style='border-collapse: collapse; margin-bottom: 20px;'>\n";
        echo "<tr><th>TR</th><th>ag (m/s²)</th><th>ag (g)</th><th>F0</th><th>Tc*</th></tr>\n";
        
        $TR_values = [30, 50, 72, 101, 140, 201, 475, 975, 2475];
        foreach ($TR_values as $tr) {
            $ag_ms2 = $point["ag_$tr"];
            $ag_g = $ag_ms2 / 9.81;
            $fo = $point["fo_$tr"];
            $tc = $point["tc_$tr"];
            
            echo "<tr>";
            echo "<td>$tr</td>";
            echo "<td>" . number_format($ag_ms2, 3) . "</td>";
            echo "<td>" . number_format($ag_g, 4) . "</td>";
            echo "<td>" . number_format($fo, 3) . "</td>";
            echo "<td>" . number_format($tc, 3) . "</td>";
            echo "</tr>\n";
        }
        echo "</table>\n";
    }
    
    // Calcola interpolazione manuale per confronto
    echo "<h3>Interpolazione Manuale per TR=1462</h3>\n";
    
    // Ordina i punti
    usort($points, function($a, $b) {
        if ($a['latitude'] != $b['latitude']) {
            return $a['latitude'] < $b['latitude'] ? -1 : 1;
        }
        return $a['longitude'] < $b['longitude'] ? -1 : 1;
    });
    
    // Coefficienti di interpolazione spaziale
    $x = ($lng - $points[0]['longitude']) / ($points[1]['longitude'] - $points[0]['longitude']);
    $y = ($lat - $points[0]['latitude']) / ($points[2]['latitude'] - $points[0]['latitude']);
    
    echo "<p>Coefficienti interpolazione spaziale: x=" . number_format($x, 6) . ", y=" . number_format($y, 6) . "</p>\n";
    
    // Interpola per TR=975 e TR=2475
    $lower_TR = 975;
    $upper_TR = 2475;
    $target_TR = 1462;
    
    // Interpolazione spaziale per TR=975
    $ag_975 = ($points[0]["ag_$lower_TR"] * (1-$x) * (1-$y) +
               $points[1]["ag_$lower_TR"] * $x * (1-$y) +
               $points[2]["ag_$lower_TR"] * (1-$x) * $y +
               $points[3]["ag_$lower_TR"] * $x * $y) / 9.81;
    
    $fo_975 = $points[0]["fo_$lower_TR"] * (1-$x) * (1-$y) +
              $points[1]["fo_$lower_TR"] * $x * (1-$y) +
              $points[2]["fo_$lower_TR"] * (1-$x) * $y +
              $points[3]["fo_$lower_TR"] * $x * $y;
    
    $tc_975 = $points[0]["tc_$lower_TR"] * (1-$x) * (1-$y) +
              $points[1]["tc_$lower_TR"] * $x * (1-$y) +
              $points[2]["tc_$lower_TR"] * (1-$x) * $y +
              $points[3]["tc_$lower_TR"] * $x * $y;
    
    // Interpolazione spaziale per TR=2475
    $ag_2475 = ($points[0]["ag_$upper_TR"] * (1-$x) * (1-$y) +
                $points[1]["ag_$upper_TR"] * $x * (1-$y) +
                $points[2]["ag_$upper_TR"] * (1-$x) * $y +
                $points[3]["ag_$upper_TR"] * $x * $y) / 9.81;
    
    $fo_2475 = $points[0]["fo_$upper_TR"] * (1-$x) * (1-$y) +
               $points[1]["fo_$upper_TR"] * $x * (1-$y) +
               $points[2]["fo_$upper_TR"] * (1-$x) * $y +
               $points[3]["fo_$upper_TR"] * $x * $y;
    
    $tc_2475 = $points[0]["tc_$upper_TR"] * (1-$x) * (1-$y) +
               $points[1]["tc_$upper_TR"] * $x * (1-$y) +
               $points[2]["tc_$upper_TR"] * (1-$x) * $y +
               $points[3]["tc_$upper_TR"] * $x * $y;
    
    echo "<table border='1' style='border-collapse: collapse;'>\n";
    echo "<tr><th>Parametro</th><th>TR=975 (interpolato)</th><th>TR=2475 (interpolato)</th></tr>\n";
    echo "<tr><td>ag (g)</td><td>" . number_format($ag_975, 4) . "</td><td>" . number_format($ag_2475, 4) . "</td></tr>\n";
    echo "<tr><td>F0</td><td>" . number_format($fo_975, 3) . "</td><td>" . number_format($fo_2475, 3) . "</td></tr>\n";
    echo "<tr><td>Tc*</td><td>" . number_format($tc_975, 3) . "</td><td>" . number_format($tc_2475, 3) . "</td></tr>\n";
    echo "</table>\n";
    
    // Interpolazione temporale per TR=1462
    $x_time = (log($target_TR) - log($lower_TR)) / (log($upper_TR) - log($lower_TR));
    echo "<p>Coefficiente interpolazione temporale: x_time=" . number_format($x_time, 6) . "</p>\n";
    
    $ag_1462 = $ag_975 + $x_time * ($ag_2475 - $ag_975);
    $fo_1462 = $fo_975 + $x_time * ($fo_2475 - $fo_975);
    $tc_1462 = $tc_975 + $x_time * ($tc_2475 - $tc_975);
    
    echo "<h3>Risultato Finale per TR=1462</h3>\n";
    echo "<table border='1' style='border-collapse: collapse;'>\n";
    echo "<tr><th>Parametro</th><th>Valore Calcolato</th><th>Valore Atteso</th><th>Differenza</th><th>% Errore</th></tr>\n";
    
    $ag_expected = 0.232;
    $fo_expected = 2.504;
    $tc_expected = 0.362;
    
    $ag_error = abs($ag_1462 - $ag_expected) / $ag_expected * 100;
    $fo_error = abs($fo_1462 - $fo_expected) / $fo_expected * 100;
    $tc_error = abs($tc_1462 - $tc_expected) / $tc_expected * 100;
    
    echo "<tr><td>ag (g)</td><td>" . number_format($ag_1462, 4) . "</td><td>" . number_format($ag_expected, 4) . "</td><td>" . number_format(abs($ag_1462 - $ag_expected), 4) . "</td><td>" . number_format($ag_error, 1) . "%</td></tr>\n";
    echo "<tr><td>F0</td><td>" . number_format($fo_1462, 3) . "</td><td>" . number_format($fo_expected, 3) . "</td><td>" . number_format(abs($fo_1462 - $fo_expected), 3) . "</td><td>" . number_format($fo_error, 1) . "%</td></tr>\n";
    echo "<tr><td>Tc*</td><td>" . number_format($tc_1462, 3) . "</td><td>" . number_format($tc_expected, 3) . "</td><td>" . number_format(abs($tc_1462 - $tc_expected), 3) . "</td><td>" . number_format($tc_error, 1) . "%</td></tr>\n";
    echo "</table>\n";
    
    // Verifica se i valori di riferimento potrebbero essere per coordinate diverse
    echo "<h3>Verifica Coordinate Esatte</h3>\n";
    echo "<p>Potrebbe essere che i valori di riferimento siano per coordinate leggermente diverse.</p>\n";
    echo "<p>Proviamo a cercare nel database coordinate che producano valori più vicini a quelli attesi.</p>\n";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>Errore: " . $e->getMessage() . "</p>\n";
}
?>
