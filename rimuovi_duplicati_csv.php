<?php
/**
 * Script per rimuovere righe duplicate dal CSV pulito
 * Mantiene solo la prima occorrenza di ogni ID
 */

echo "=== RIMOZIONE DUPLICATI DA CSV PULITO ===\n\n";

$input_file = 'docs/analisi_normative/conversione_pulito.CSV';
$output_file = 'docs/analisi_normative/conversione_finale.CSV';

try {
    // Verifica esistenza file
    if (!file_exists($input_file)) {
        die("ERRORE: File $input_file non trovato\n");
    }
    
    echo "1. Apertura file CSV pulito...\n";
    echo "   Input: $input_file\n";
    echo "   Output: $output_file\n";
    echo "   Dimensione input: " . number_format(filesize($input_file)) . " bytes\n\n";
    
    // Apri file di input
    $handle_input = fopen($input_file, 'r');
    if (!$handle_input) {
        die("ERRORE: Impossibile aprire il file di input\n");
    }
    
    // Crea file di output
    $handle_output = fopen($output_file, 'w');
    if (!$handle_output) {
        fclose($handle_input);
        die("ERRORE: Impossibile creare il file di output\n");
    }
    
    echo "2. Analisi e rimozione duplicati...\n";
    
    $total_rows = 0;
    $unique_rows = 0;
    $duplicate_rows = 0;
    $seen_ids = [];
    $duplicate_examples = [];
    
    while (($row = fgetcsv($handle_input, 0, ';')) !== FALSE) {
        $total_rows++;
        
        // Estrai ID (prima colonna)
        $id = trim($row[0]);
        
        // Rimuovi BOM se presente
        $id = str_replace("\xEF\xBB\xBF", "", $id);
        
        // Controlla se ID già visto
        if (isset($seen_ids[$id])) {
            $duplicate_rows++;
            
            // Salva esempi di duplicati (primi 10)
            if (count($duplicate_examples) < 10) {
                $duplicate_examples[] = [
                    'id' => $id,
                    'row' => $total_rows,
                    'first_seen' => $seen_ids[$id]
                ];
            }
            
            // Salta questa riga (duplicato)
            continue;
        }
        
        // Marca ID come visto
        $seen_ids[$id] = $total_rows;
        
        // Scrivi riga nel file di output
        fputcsv($handle_output, $row, ';');
        $unique_rows++;
        
        if ($total_rows % 1000 == 0) {
            echo "   Processate $total_rows righe, uniche: $unique_rows, duplicate: $duplicate_rows...\n";
        }
    }
    
    fclose($handle_input);
    fclose($handle_output);
    
    echo "\n3. Risultati rimozione duplicati...\n";
    echo "   Righe totali input: $total_rows\n";
    echo "   Righe uniche output: $unique_rows\n";
    echo "   Righe duplicate rimosse: $duplicate_rows\n";
    echo "   Percentuale duplicati: " . round(($duplicate_rows / $total_rows) * 100, 2) . "%\n";
    echo "   File output: $output_file\n";
    echo "   Dimensione output: " . number_format(filesize($output_file)) . " bytes\n\n";
    
    // Mostra esempi di duplicati
    if (!empty($duplicate_examples)) {
        echo "4. Esempi di duplicati rimossi...\n";
        foreach ($duplicate_examples as $dup) {
            echo "   ID {$dup['id']}: prima occorrenza riga {$dup['first_seen']}, duplicato riga {$dup['row']}\n";
        }
        echo "\n";
    }
    
    // Verifica file finale
    echo "5. Verifica file finale...\n";
    
    $handle_verify = fopen($output_file, 'r');
    if ($handle_verify) {
        $verify_count = 0;
        $verify_ids = [];
        $verify_duplicates = 0;
        
        while (($row = fgetcsv($handle_verify, 0, ';')) !== FALSE) {
            $verify_count++;
            $id = trim(str_replace("\xEF\xBB\xBF", "", $row[0]));
            
            if (isset($verify_ids[$id])) {
                $verify_duplicates++;
            } else {
                $verify_ids[$id] = true;
            }
        }
        
        fclose($handle_verify);
        
        echo "   Righe nel file finale: $verify_count\n";
        echo "   ID unici: " . count($verify_ids) . "\n";
        echo "   Duplicati rimanenti: $verify_duplicates\n";
        
        if ($verify_duplicates == 0) {
            echo "   ✅ NESSUN DUPLICATO nel file finale!\n";
        } else {
            echo "   ❌ Ancora $verify_duplicates duplicati nel file finale\n";
        }
        
        // Verifica ID 10007
        if (isset($verify_ids['10007'])) {
            echo "   ✅ ID 10007 presente nel file finale\n";
        } else {
            echo "   ⚠ ID 10007 NON presente nel file finale\n";
        }
    }
    
    echo "\n6. Riepilogo...\n";
    echo "   " . str_repeat("=", 50) . "\n";
    
    if ($duplicate_rows > 0) {
        echo "   ✅ DUPLICATI RIMOSSI: $duplicate_rows righe\n";
        echo "   ✅ FILE PULITO: $unique_rows righe uniche\n";
        echo "   ✅ PRONTO PER IMPORTAZIONE: $output_file\n";
    } else {
        echo "   ✅ NESSUN DUPLICATO TROVATO\n";
        echo "   ✅ FILE GIÀ PULITO: $output_file\n";
    }
    
    echo "\n=== RIMOZIONE DUPLICATI COMPLETATA ===\n";
    echo "Usa il file: $output_file per l'importazione\n";
    
} catch (Exception $e) {
    echo "ERRORE: " . $e->getMessage() . "\n";
    if (isset($handle_input)) fclose($handle_input);
    if (isset($handle_output)) fclose($handle_output);
}
?>
