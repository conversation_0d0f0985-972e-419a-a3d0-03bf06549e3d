<?php
/**
 * Script per rimuovere la colonna vuota (posizione 26) dal file CSV
 * Crea un nuovo file CSV pulito senza la colonna vuota
 */

echo "=== PULIZIA CSV - RIMOZIONE COLONNA VUOTA ===\n\n";

$input_file = 'docs/analisi_normative/conversione.CSV';
$output_file = 'docs/analisi_normative/conversione_pulito.CSV';

try {
    // Verifica esistenza file di input
    if (!file_exists($input_file)) {
        die("ERRORE: File $input_file non trovato\n");
    }
    
    echo "1. Informazioni file di input...\n";
    echo "   File input: $input_file\n";
    echo "   Dimensione: " . number_format(filesize($input_file)) . " bytes\n\n";
    
    // Apri file di input
    echo "2. Apertura file CSV...\n";
    $handle_input = fopen($input_file, 'r');
    if (!$handle_input) {
        die("ERRORE: Impossibile aprire il file di input\n");
    }
    
    // Crea file di output
    $handle_output = fopen($output_file, 'w');
    if (!$handle_output) {
        fclose($handle_input);
        die("ERRORE: Impossibile creare il file di output\n");
    }
    
    echo "   ✓ File di input aperto\n";
    echo "   ✓ File di output creato: $output_file\n\n";
    
    // Analizza prima riga per identificare la struttura
    echo "3. Analisi struttura prima riga...\n";
    $first_row = fgetcsv($handle_input, 0, ';');
    if ($first_row === FALSE) {
        die("ERRORE: Impossibile leggere la prima riga\n");
    }
    
    echo "   Colonne originali: " . count($first_row) . "\n";
    echo "   Prima riga: " . implode(';', array_slice($first_row, 0, 10)) . "...\n";
    
    // Identifica la colonna vuota (posizione 26)
    $empty_column = 26;
    echo "   Colonna vuota identificata: posizione $empty_column\n";
    
    if (count($first_row) > $empty_column && trim($first_row[$empty_column]) === '') {
        echo "   ✓ Confermata colonna vuota alla posizione $empty_column\n";
    } else {
        echo "   ⚠ Colonna alla posizione $empty_column non è vuota: '" . (isset($first_row[$empty_column]) ? $first_row[$empty_column] : 'N/A') . "'\n";
    }
    
    // Rimuovi colonna vuota dalla prima riga
    $cleaned_first_row = $first_row;
    if (count($cleaned_first_row) > $empty_column) {
        array_splice($cleaned_first_row, $empty_column, 1);
    }
    
    echo "   Colonne dopo pulizia: " . count($cleaned_first_row) . "\n\n";
    
    // Scrivi prima riga pulita
    fputcsv($handle_output, $cleaned_first_row, ';');
    
    // Riposiziona all'inizio del file di input
    rewind($handle_input);
    
    // Processa tutte le righe
    echo "4. Processamento righe...\n";
    
    $total_rows = 0;
    $processed_rows = 0;
    $empty_columns_found = 0;
    
    while (($row = fgetcsv($handle_input, 0, ';')) !== FALSE) {
        $total_rows++;
        
        // Verifica se la colonna è effettivamente vuota
        $is_empty = (count($row) > $empty_column && trim($row[$empty_column]) === '');
        if ($is_empty) {
            $empty_columns_found++;
        }
        
        // Rimuovi la colonna vuota
        $cleaned_row = $row;
        if (count($cleaned_row) > $empty_column) {
            array_splice($cleaned_row, $empty_column, 1);
        }
        
        // Scrivi riga pulita
        fputcsv($handle_output, $cleaned_row, ';');
        $processed_rows++;
        
        // Mostra progresso ogni 1000 righe
        if ($total_rows % 1000 == 0) {
            echo "   Processate $total_rows righe, colonne vuote trovate: $empty_columns_found...\n";
        }
    }
    
    fclose($handle_input);
    fclose($handle_output);
    
    echo "\n5. Risultati pulizia...\n";
    echo "   Righe totali processate: $total_rows\n";
    echo "   Righe scritte nel file pulito: $processed_rows\n";
    echo "   Colonne vuote trovate: $empty_columns_found\n";
    echo "   File output: $output_file\n";
    echo "   Dimensione file output: " . number_format(filesize($output_file)) . " bytes\n\n";
    
    // Verifica struttura file pulito
    echo "6. Verifica file pulito...\n";
    
    $handle_verify = fopen($output_file, 'r');
    if ($handle_verify) {
        // Leggi prima riga per verifica
        $verify_row = fgetcsv($handle_verify, 0, ';');
        if ($verify_row) {
            echo "   Colonne nel file pulito: " . count($verify_row) . "\n";
            echo "   Prima riga pulita: " . implode(';', array_slice($verify_row, 0, 10)) . "...\n";
            
            // Verifica che non ci siano più colonne vuote alla posizione 26
            if (count($verify_row) > 26) {
                echo "   Colonna 26 nel file pulito: '" . trim($verify_row[26]) . "'\n";
                if (trim($verify_row[26]) !== '') {
                    echo "   ✓ Colonna 26 ora contiene dati (non più vuota)\n";
                } else {
                    echo "   ⚠ Colonna 26 ancora vuota\n";
                }
            }
        }
        
        // Leggi alcune righe di esempio
        echo "\n   Esempio righe pulite:\n";
        rewind($handle_verify);
        for ($i = 0; $i < 3; $i++) {
            $example_row = fgetcsv($handle_verify, 0, ';');
            if ($example_row) {
                echo "   Riga " . ($i + 1) . " (" . count($example_row) . " colonne): ";
                echo implode(';', array_slice($example_row, 0, 8)) . "...\n";
            }
        }
        
        fclose($handle_verify);
    }
    
    // Nuova mappatura per il file pulito
    echo "\n7. Nuova mappatura per file pulito...\n";
    echo "   MAPPATURA CORRETTA (senza colonna vuota):\n";
    
    $new_mapping = [
        30   => ['ag' => 3,  'fo' => 4,  'tc' => 5],
        50   => ['ag' => 6,  'fo' => 7,  'tc' => 8],
        72   => ['ag' => 9,  'fo' => 10, 'tc' => 11],
        101  => ['ag' => 12, 'fo' => 13, 'tc' => 14],
        140  => ['ag' => 15, 'fo' => 16, 'tc' => 17],
        201  => ['ag' => 18, 'fo' => 19, 'tc' => 20],
        475  => ['ag' => 21, 'fo' => 22, 'tc' => 23],
        975  => ['ag' => 24, 'fo' => 25, 'tc' => 26],  // Ora TC=26 (era 27)
        2475 => ['ag' => 27, 'fo' => 28, 'tc' => 29]   // Ora tutto spostato di -1
    ];
    
    foreach ($new_mapping as $tr => $cols) {
        echo "     TR $tr: AG=col{$cols['ag']}, FO=col{$cols['fo']}, TC=col{$cols['tc']}\n";
    }
    
    echo "\n8. Riepilogo...\n";
    echo "   ✅ File CSV pulito creato con successo\n";
    echo "   ✅ Colonna vuota rimossa dalla posizione 26\n";
    echo "   ✅ Struttura dati ora corretta e consistente\n";
    echo "   ✅ Pronto per importazione ottimizzata\n";
    
    if ($empty_columns_found > ($total_rows * 0.9)) {
        echo "   ✅ Colonna effettivamente vuota nella maggior parte delle righe\n";
    } else {
        echo "   ⚠ Colonna non sempre vuota - verifica necessaria\n";
    }
    
    echo "\n=== PULIZIA CSV COMPLETATA ===\n";
    echo "File pulito disponibile: $output_file\n";
    
} catch (Exception $e) {
    echo "ERRORE: " . $e->getMessage() . "\n";
    if (isset($handle_input)) fclose($handle_input);
    if (isset($handle_output)) fclose($handle_output);
}
?>
