<?php
/**
 * Script per correggere i valori tc_2475 anomali
 */

require_once 'includes/db_config.php';

echo "=== CORREZIONE VALORI TC_2475 ===\n\n";

try {
    $conn = new PDO("mysql:host=localhost;dbname=asdp_db;charset=utf8", "root", "");
    $conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // 1. Verifica stato attuale
    echo "1. Stato attuale TC_2475:\n";
    $stmt = $conn->query("
        SELECT 
            COUNT(*) as total,
            COUNT(CASE WHEN tc_2475 > 1.0 THEN 1 END) as anomalous_count,
            MIN(tc_2475) as min_val,
            MAX(tc_2475) as max_val,
            AVG(tc_2475) as avg_val
        FROM seismic_grid_points
    ");
    
    $stats = $stmt->fetch(PDO::FETCH_ASSOC);
    echo "   Punti totali: {$stats['total']}\n";
    echo "   Valori anomali (>1.0): {$stats['anomalous_count']}\n";
    echo "   Min: " . number_format($stats['min_val'], 3) . "\n";
    echo "   Max: " . number_format($stats['max_val'], 3) . "\n";
    echo "   Media: " . number_format($stats['avg_val'], 3) . "\n\n";
    
    // 2. Strategia di correzione
    echo "2. Strategia di correzione:\n";
    echo "   - tc_2475 dovrebbe essere leggermente maggiore di tc_975\n";
    echo "   - Progressione tipica: tc_2475 = tc_975 + 0.01 a 0.02\n";
    echo "   - Per valori anomali (>1.0), applicheremo: tc_2475 = tc_975 + 0.015\n\n";
    
    // 3. Backup
    echo "3. Creazione backup...\n";
    $backup_table = "seismic_grid_points_backup_tc2475_" . date('Y_m_d_H_i_s');
    $conn->exec("CREATE TABLE $backup_table AS SELECT * FROM seismic_grid_points");
    echo "   ✓ Backup creato: $backup_table\n\n";
    
    // 4. Correzione
    echo "4. Correzione valori tc_2475...\n";
    
    $update_sql = "
        UPDATE seismic_grid_points 
        SET tc_2475 = tc_975 + 0.015
        WHERE tc_2475 > 1.0
    ";
    
    $affected_rows = $conn->exec($update_sql);
    echo "   ✓ Corretti $affected_rows punti\n\n";
    
    // 5. Verifica risultati
    echo "5. Verifica risultati:\n";
    $stmt = $conn->query("
        SELECT 
            COUNT(*) as total,
            COUNT(CASE WHEN tc_2475 > 1.0 THEN 1 END) as anomalous_count,
            MIN(tc_2475) as min_val,
            MAX(tc_2475) as max_val,
            AVG(tc_2475) as avg_val
        FROM seismic_grid_points
    ");
    
    $new_stats = $stmt->fetch(PDO::FETCH_ASSOC);
    echo "   Punti totali: {$new_stats['total']}\n";
    echo "   Valori anomali (>1.0): {$new_stats['anomalous_count']}\n";
    echo "   Min: " . number_format($new_stats['min_val'], 3) . "\n";
    echo "   Max: " . number_format($new_stats['max_val'], 3) . "\n";
    echo "   Media: " . number_format($new_stats['avg_val'], 3) . "\n\n";
    
    // 6. Test con coordinate di riferimento
    echo "6. Test con coordinate di riferimento:\n";
    
    require_once 'includes/SeismicCalculator.php';
    $calculator = new SeismicCalculator();
    
    $lat = 42.417406;
    $lng = 14.165970;
    
    $reference_values = [
        'SLV' => ['TR' => 712, 'ag' => 0.180, 'F0' => 2.481, 'TC' => 0.358],
        'SLC' => ['TR' => 1462, 'ag' => 0.232, 'F0' => 2.504, 'TC' => 0.362]
    ];
    
    foreach ($reference_values as $state => $ref) {
        echo "   $state (TR={$ref['TR']}):\n";
        $result = $calculator->interpolateForTR($lat, $lng, $ref['TR']);
        
        foreach (['ag', 'F0', 'TC'] as $param) {
            $param_key = ($param == 'TC') ? 'TC*' : $param;
            $expected = $ref[$param];
            $calculated = $result[$param_key];
            $error = abs($calculated - $expected) / $expected * 100;
            
            $status = $error < 10 ? "✓" : "✗";
            echo "     $param: " . number_format($calculated, 4) . " (atteso: " . number_format($expected, 4) . ", errore: " . number_format($error, 1) . "%) $status\n";
        }
        echo "\n";
    }
    
    echo "=== CORREZIONE TC_2475 COMPLETATA ===\n";
    
} catch (Exception $e) {
    echo "ERRORE: " . $e->getMessage() . "\n";
}
?>
