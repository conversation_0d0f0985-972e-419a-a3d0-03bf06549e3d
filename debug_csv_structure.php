<?php
/**
 * Script per analizzare la struttura del file CSV e identificare il problema
 */

echo "=== DEBUG STRUTTURA CSV ===\n\n";

$csv_file = 'docs/analisi_normative/conversione.CSV';

try {
    if (!file_exists($csv_file)) {
        die("ERRORE: File $csv_file non trovato\n");
    }
    
    echo "1. Informazioni file:\n";
    echo "   File: $csv_file\n";
    echo "   Dimensione: " . number_format(filesize($csv_file)) . " bytes\n\n";
    
    // Apri file CSV
    $handle = fopen($csv_file, 'r');
    if (!$handle) {
        die("ERRORE: Impossibile aprire il file CSV\n");
    }
    
    echo "2. Analisi prime 15 righe:\n";
    echo str_repeat("=", 120) . "\n";
    
    for ($i = 0; $i < 15; $i++) {
        $row = fgetcsv($handle, 0, ';');
        if ($row === FALSE) break;
        
        echo "Riga " . ($i + 1) . " (" . count($row) . " colonne):\n";
        
        // Mostra le prime 10 colonne
        for ($j = 0; $j < min(10, count($row)); $j++) {
            $cell = trim($row[$j]);
            if (strlen($cell) > 15) {
                $cell = substr($cell, 0, 12) . "...";
            }
            echo sprintf("  [%2d] %-15s", $j, $cell);
            if (($j + 1) % 5 == 0) echo "\n";
        }
        if (count($row) > 10) {
            echo "  ... (+" . (count($row) - 10) . " colonne)\n";
        }
        echo "\n";
    }
    
    // Riposiziona all'inizio
    rewind($handle);
    
    echo "\n3. Analisi riga dati (riga 12):\n";
    echo str_repeat("=", 120) . "\n";
    
    // Salta le prime 11 righe
    for ($i = 0; $i < 11; $i++) {
        fgetcsv($handle, 0, ';');
    }
    
    // Leggi la prima riga di dati
    $data_row = fgetcsv($handle, 0, ';');
    if ($data_row !== FALSE) {
        echo "Prima riga dati (" . count($data_row) . " colonne):\n";
        
        for ($j = 0; $j < count($data_row); $j++) {
            $cell = trim($data_row[$j]);
            echo sprintf("[%2d] %-15s", $j, $cell);
            if (($j + 1) % 5 == 0) echo "\n";
        }
        echo "\n\n";
        
        // Analizza coordinate
        echo "4. Analisi coordinate:\n";
        if (count($data_row) > 2) {
            $id = trim($data_row[0]);
            $lng_str = str_replace(',', '.', trim($data_row[1]));
            $lat_str = str_replace(',', '.', trim($data_row[2]));
            
            $lng = floatval($lng_str);
            $lat = floatval($lat_str);
            
            echo "   ID: $id\n";
            echo "   LON (col 1): '$lng_str' -> $lng\n";
            echo "   LAT (col 2): '$lat_str' -> $lat\n";
            
            if ($lat >= 35 && $lat <= 48 && $lng >= 6 && $lng <= 19) {
                echo "   ✓ Coordinate valide per l'Italia\n";
            } else {
                echo "   ✗ Coordinate NON valide per l'Italia\n";
            }
        }
        
        // Analizza primi valori sismici (TR=30)
        echo "\n5. Analisi valori sismici TR=30 (colonne 3-5):\n";
        if (count($data_row) > 5) {
            $ag_str = str_replace(',', '.', trim($data_row[3]));
            $fo_str = str_replace(',', '.', trim($data_row[4]));
            $tc_str = str_replace(',', '.', trim($data_row[5]));
            
            $ag = floatval($ag_str);
            $fo = floatval($fo_str);
            $tc = floatval($tc_str);
            
            echo "   AG (col 3): '$ag_str' -> $ag\n";
            echo "   FO (col 4): '$fo_str' -> $fo\n";
            echo "   TC (col 5): '$tc_str' -> $tc\n";
            
            if ($ag > 0 && $fo > 0 && $tc > 0) {
                echo "   ✓ Valori sismici validi\n";
            } else {
                echo "   ✗ Valori sismici NON validi\n";
            }
        }
        
        // Analizza valori sismici TR=975 (colonne 24, 25, 27)
        echo "\n6. Analisi valori sismici TR=975 (colonne 24, 25, 27):\n";
        if (count($data_row) > 27) {
            $ag_str = str_replace(',', '.', trim($data_row[24]));
            $fo_str = str_replace(',', '.', trim($data_row[25]));
            $tc_str = str_replace(',', '.', trim($data_row[27]));
            
            $ag = floatval($ag_str);
            $fo = floatval($fo_str);
            $tc = floatval($tc_str);
            
            echo "   AG (col 24): '$ag_str' -> $ag\n";
            echo "   FO (col 25): '$fo_str' -> $fo\n";
            echo "   TC (col 27): '$tc_str' -> $tc\n";
            
            if ($ag > 0 && $fo > 0 && $tc > 0) {
                echo "   ✓ Valori sismici validi\n";
            } else {
                echo "   ✗ Valori sismici NON validi\n";
            }
        }
        
        // Verifica colonna 26 (dovrebbe essere vuota)
        echo "\n7. Verifica colonna 26 (dovrebbe essere vuota):\n";
        if (count($data_row) > 26) {
            $col26 = trim($data_row[26]);
            echo "   Colonna 26: '$col26'\n";
            if (empty($col26)) {
                echo "   ✓ Colonna 26 vuota (corretto)\n";
            } else {
                echo "   ⚠ Colonna 26 NON vuota: potrebbe essere un problema di mappatura\n";
            }
        }
    }
    
    fclose($handle);
    
    echo "\n8. Test connessione database:\n";
    $conn = new PDO("mysql:host=localhost;dbname=asdp_db;charset=utf8", "root", "");
    
    // Cerca un punto vicino alle coordinate del file
    if (isset($lat) && isset($lng)) {
        $stmt = $conn->prepare("
            SELECT id, latitude, longitude, 
                   ABS(latitude - ?) + ABS(longitude - ?) as distance
            FROM seismic_grid_points 
            ORDER BY distance 
            LIMIT 1
        ");
        $stmt->execute([$lat, $lng]);
        $nearest = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($nearest) {
            echo "   Punto più vicino nel DB:\n";
            echo "     ID: {$nearest['id']}\n";
            echo "     Coordinate: {$nearest['latitude']}, {$nearest['longitude']}\n";
            echo "     Distanza: " . number_format($nearest['distance'], 6) . "\n";
            
            if ($nearest['distance'] < 0.001) {
                echo "   ✓ Match trovato (distanza < 0.001)\n";
            } else {
                echo "   ⚠ Nessun match preciso trovato\n";
            }
        }
    }
    
    echo "\n=== DEBUG COMPLETATO ===\n";
    
} catch (Exception $e) {
    echo "ERRORE: " . $e->getMessage() . "\n";
}
?>
