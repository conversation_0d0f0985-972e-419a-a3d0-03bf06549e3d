<?php
/**
 * <PERSON>ript finale per importare i dati dal file conversione.CSV
 * File: docs/analisi_normative/conversione.CSV
 * Formato: CSV con separatore ; e decimali ,
 */

require_once 'includes/db_config.php';

echo "=== IMPORTAZIONE FINALE DA CONVERSIONE.CSV ===\n\n";

$csv_file = 'docs/analisi_normative/conversione.CSV';

try {
    // Verifica esistenza file
    if (!file_exists($csv_file)) {
        die("ERRORE: File $csv_file non trovato\n");
    }
    
    echo "1. Apertura file CSV...\n";
    echo "   File: $csv_file\n";
    echo "   Dimensione: " . number_format(filesize($csv_file)) . " bytes\n\n";
    
    // Connessione database
    echo "2. Connessione database...\n";
    $conn = new PDO("mysql:host=localhost;dbname=asdp_db;charset=utf8", "root", "");
    $conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "   ✓ Connesso al database\n\n";
    
    // Backup completo
    echo "3. Creazione backup completo...\n";
    $backup_table = "seismic_grid_points_backup_csv_" . date('Y_m_d_H_i_s');
    $conn->exec("CREATE TABLE $backup_table AS SELECT * FROM seismic_grid_points");
    echo "   ✓ Backup creato: $backup_table\n\n";
    
    // Apri file CSV
    echo "4. Lettura file CSV...\n";
    $handle = fopen($csv_file, 'r');
    if (!$handle) {
        die("ERRORE: Impossibile aprire il file CSV\n");
    }
    
    // Salta le righe di header (prime 11 righe)
    for ($i = 0; $i < 11; $i++) {
        fgetcsv($handle, 0, ';');
    }
    echo "   ✓ Header saltato (11 righe)\n";
    
    // Mappatura colonne (basata sull'analisi del file)
    // ID;LON;LAT;ag;Fo;TC;ag;Fo;TC;ag;Fo;TC;ag;Fo;TC;ag;Fo;TC;ag;Fo;TC;ag;Fo;TC;ag;Fo;;TC;ag;Fo;TC
    $tr_mapping = [
        30   => ['ag' => 3,  'fo' => 4,  'tc' => 5],
        50   => ['ag' => 6,  'fo' => 7,  'tc' => 8],
        72   => ['ag' => 9,  'fo' => 10, 'tc' => 11],
        101  => ['ag' => 12, 'fo' => 13, 'tc' => 14],
        140  => ['ag' => 15, 'fo' => 16, 'tc' => 17],
        201  => ['ag' => 18, 'fo' => 19, 'tc' => 20],
        475  => ['ag' => 21, 'fo' => 22, 'tc' => 23],
        975  => ['ag' => 24, 'fo' => 25, 'tc' => 27],  // Nota: TC=27 (colonna vuota in 26)
        2475 => ['ag' => 28, 'fo' => 29, 'tc' => 30]
    ];
    
    echo "   Mappatura TR:\n";
    foreach ($tr_mapping as $tr => $cols) {
        echo "     TR $tr: AG=col{$cols['ag']}, FO=col{$cols['fo']}, TC=col{$cols['tc']}\n";
    }
    echo "\n";
    
    // Prepara statement di update
    echo "5. Preparazione statement SQL...\n";
    $update_statements = [];
    
    foreach ($tr_mapping as $tr => $columns) {
        $update_statements[$tr] = $conn->prepare("
            UPDATE seismic_grid_points 
            SET ag_$tr = ?, fo_$tr = ?, tc_$tr = ?
            WHERE ABS(latitude - ?) < 0.001 AND ABS(longitude - ?) < 0.001
        ");
        echo "   ✓ Statement preparato per TR=$tr\n";
    }
    echo "\n";
    
    // Importazione dati
    echo "6. Importazione dati...\n";
    
    $total_rows = 0;
    $updated_rows = 0;
    $skipped_rows = 0;
    $error_rows = 0;
    
    while (($row = fgetcsv($handle, 0, ';')) !== FALSE) {
        $total_rows++;
        
        // Verifica che la riga abbia abbastanza colonne
        if (count($row) < 31) {
            $skipped_rows++;
            continue;
        }
        
        // Estrai coordinate (colonne 1 e 2)
        $lng_str = str_replace(',', '.', trim($row[1])); // Converti virgola in punto
        $lat_str = str_replace(',', '.', trim($row[2])); // Converti virgola in punto
        
        $lng = floatval($lng_str);
        $lat = floatval($lat_str);
        
        // Verifica validità coordinate Italia
        if ($lat < 35 || $lat > 48 || $lng < 6 || $lng > 19) {
            $skipped_rows++;
            continue;
        }
        
        // Aggiorna i dati per ogni TR
        $row_updated = false;
        $tr_updated_count = 0;
        
        foreach ($tr_mapping as $tr => $columns) {
            try {
                // Estrai valori sismici
                $ag_str = str_replace(',', '.', trim($row[$columns['ag']]));
                $fo_str = str_replace(',', '.', trim($row[$columns['fo']]));
                $tc_str = str_replace(',', '.', trim($row[$columns['tc']]));
                
                $ag = floatval($ag_str);
                $fo = floatval($fo_str);
                $tc = floatval($tc_str);
                
                // Verifica validità dati sismici
                if ($ag > 0 && $fo > 0 && $tc > 0) {
                    // Converti ag da g a m/s² se necessario
                    if ($ag < 1.0) {
                        $ag = $ag * 9.81;
                    }
                    
                    $update_statements[$tr]->execute([$ag, $fo, $tc, $lat, $lng]);
                    if ($update_statements[$tr]->rowCount() > 0) {
                        $row_updated = true;
                        $tr_updated_count++;
                    }
                }
            } catch (Exception $e) {
                // Errore su questo TR, continua con gli altri
                continue;
            }
        }
        
        if ($row_updated) {
            $updated_rows++;
        } else {
            if ($tr_updated_count == 0) {
                $error_rows++;
            }
        }
        
        // Mostra progresso ogni 1000 righe
        if ($total_rows % 1000 == 0) {
            echo "   Processate $total_rows righe, aggiornate $updated_rows, saltate $skipped_rows, errori $error_rows...\n";
        }
    }
    
    fclose($handle);
    
    echo "\n7. Risultati importazione...\n";
    echo "   Righe totali processate: $total_rows\n";
    echo "   Righe aggiornate: $updated_rows\n";
    echo "   Righe saltate: $skipped_rows\n";
    echo "   Righe con errori: $error_rows\n\n";
    
    // Verifica stato database
    echo "8. Verifica stato database...\n";
    
    // Verifica tc_975
    $stmt = $conn->query("
        SELECT 
            COUNT(*) as total,
            COUNT(CASE WHEN tc_975 = 0 THEN 1 END) as zero_count,
            MIN(tc_975) as min_val,
            MAX(tc_975) as max_val,
            AVG(tc_975) as avg_val
        FROM seismic_grid_points
    ");
    
    $stats975 = $stmt->fetch(PDO::FETCH_ASSOC);
    echo "   TC_975 - Totali: {$stats975['total']}, Zero: {$stats975['zero_count']}\n";
    echo "   TC_975 - Range: " . number_format($stats975['min_val'], 3) . " - " . number_format($stats975['max_val'], 3) . " (media: " . number_format($stats975['avg_val'], 3) . ")\n";
    
    // Verifica tc_2475
    $stmt = $conn->query("
        SELECT 
            COUNT(CASE WHEN tc_2475 > 1.0 THEN 1 END) as anomalous_count,
            COUNT(CASE WHEN tc_2475 <= 1.0 AND tc_2475 > 0 THEN 1 END) as normal_count,
            MIN(tc_2475) as min_val,
            MAX(tc_2475) as max_val,
            AVG(tc_2475) as avg_val
        FROM seismic_grid_points
    ");
    
    $stats2475 = $stmt->fetch(PDO::FETCH_ASSOC);
    echo "   TC_2475 - Normali (≤1.0): {$stats2475['normal_count']}, Anomali (>1.0): {$stats2475['anomalous_count']}\n";
    echo "   TC_2475 - Range: " . number_format($stats2475['min_val'], 3) . " - " . number_format($stats2475['max_val'], 3) . " (media: " . number_format($stats2475['avg_val'], 3) . ")\n\n";
    
    // Test finale con coordinate di riferimento
    echo "9. Test finale con coordinate di riferimento...\n";
    
    require_once 'includes/SeismicCalculator.php';
    $calculator = new SeismicCalculator();
    
    $lat = 42.417406;
    $lng = 14.165970;
    
    $reference_values = [
        'SLO' => ['TR' => 45, 'ag' => 0.058, 'F0' => 2.469, 'TC' => 0.302],
        'SLD' => ['TR' => 75, 'ag' => 0.072, 'F0' => 2.461, 'TC' => 0.323],
        'SLV' => ['TR' => 712, 'ag' => 0.180, 'F0' => 2.481, 'TC' => 0.358],
        'SLC' => ['TR' => 1462, 'ag' => 0.232, 'F0' => 2.504, 'TC' => 0.362]
    ];
    
    $all_passed = true;
    
    foreach ($reference_values as $state => $ref) {
        echo "   $state (TR={$ref['TR']}):\n";
        $result = $calculator->interpolateForTR($lat, $lng, $ref['TR']);
        
        foreach (['ag', 'F0', 'TC'] as $param) {
            $param_key = ($param == 'TC') ? 'TC*' : $param;
            $expected = $ref[$param];
            $calculated = $result[$param_key];
            $error = abs($calculated - $expected) / $expected * 100;
            
            $status = $error < 10 ? "✓" : "✗";
            if ($error >= 10) $all_passed = false;
            
            echo "     $param: " . number_format($calculated, 4) . " (atteso: " . number_format($expected, 4) . ", errore: " . number_format($error, 1) . "%) $status\n";
        }
        echo "\n";
    }
    
    if ($all_passed) {
        echo "🎉 TUTTI I TEST SUPERATI! Importazione completata con successo.\n";
        echo "✅ Problema SLC risolto definitivamente!\n";
    } else {
        echo "⚠ Alcuni test non superati. Potrebbero essere necessarie ulteriori verifiche.\n";
    }
    
    echo "\n=== IMPORTAZIONE CSV COMPLETATA ===\n";
    
} catch (Exception $e) {
    echo "ERRORE: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}
?>
