<?php
/**
 * Verifica lo stato del database dopo l'aggiornamento
 */

require_once 'includes/db_config.php';

echo "=== VERIFICA STATO DATABASE ===\n\n";

try {
    $conn = new PDO("mysql:host=localhost;dbname=asdp_db;charset=utf8", "root", "");
    $conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // Verifica tc_975
    echo "1. Stato TC_975:\n";
    $stmt = $conn->query("
        SELECT 
            COUNT(*) as total,
            COUNT(CASE WHEN tc_975 = 0 THEN 1 END) as zero_count,
            MIN(tc_975) as min_val,
            MAX(tc_975) as max_val,
            AVG(tc_975) as avg_val
        FROM seismic_grid_points
    ");
    
    $stats = $stmt->fetch(PDO::FETCH_ASSOC);
    echo "   Punti totali: {$stats['total']}\n";
    echo "   Valori zero: {$stats['zero_count']}\n";
    echo "   Min: " . number_format($stats['min_val'], 3) . "\n";
    echo "   Max: " . number_format($stats['max_val'], 3) . "\n";
    echo "   Media: " . number_format($stats['avg_val'], 3) . "\n\n";
    
    // Verifica coordinate specifiche
    echo "2. Valori per coordinate di test:\n";
    $lat = 42.417406;
    $lng = 14.165970;
    
    $stmt = $conn->prepare("
        SELECT 
            latitude, longitude,
            tc_475, tc_975, tc_2475,
            POW(latitude - ?, 2) + POW(longitude - ?, 2) as distance
        FROM seismic_grid_points
        WHERE latitude BETWEEN ? - 0.1 AND ? + 0.1 
        AND longitude BETWEEN ? - 0.1 AND ? + 0.1 
        ORDER BY POW(latitude - ?, 2) + POW(longitude - ?, 2)
        LIMIT 4
    ");
    
    $stmt->execute([$lat, $lng, $lat, $lat, $lng, $lng, $lat, $lng]);
    $points = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    foreach ($points as $i => $point) {
        echo "   Punto " . ($i+1) . ": ";
        echo "Lat=" . number_format($point['latitude'], 6) . ", ";
        echo "Lng=" . number_format($point['longitude'], 6) . ", ";
        echo "tc_475=" . number_format($point['tc_475'], 3) . ", ";
        echo "tc_975=" . number_format($point['tc_975'], 3) . ", ";
        echo "tc_2475=" . number_format($point['tc_2475'], 3) . "\n";
    }
    
    // Verifica se ci sono ancora problemi con tc_2475
    echo "\n3. Problemi con TC_2475:\n";
    $stmt = $conn->query("
        SELECT COUNT(*) as count_high_tc2475
        FROM seismic_grid_points 
        WHERE tc_2475 > 1.0
    ");
    
    $high_tc2475 = $stmt->fetch(PDO::FETCH_ASSOC)['count_high_tc2475'];
    echo "   Punti con tc_2475 > 1.0: $high_tc2475\n";
    
    if ($high_tc2475 > 0) {
        echo "   Esempi di punti con tc_2475 anomalo:\n";
        $stmt = $conn->query("
            SELECT latitude, longitude, tc_475, tc_975, tc_2475
            FROM seismic_grid_points 
            WHERE tc_2475 > 1.0
            LIMIT 5
        ");
        
        $anomalous = $stmt->fetchAll(PDO::FETCH_ASSOC);
        foreach ($anomalous as $point) {
            echo "     Lat=" . number_format($point['latitude'], 6) . ", ";
            echo "Lng=" . number_format($point['longitude'], 6) . ", ";
            echo "tc_475=" . number_format($point['tc_475'], 3) . ", ";
            echo "tc_975=" . number_format($point['tc_975'], 3) . ", ";
            echo "tc_2475=" . number_format($point['tc_2475'], 3) . "\n";
        }
    }
    
} catch (Exception $e) {
    echo "ERRORE: " . $e->getMessage() . "\n";
}
?>
