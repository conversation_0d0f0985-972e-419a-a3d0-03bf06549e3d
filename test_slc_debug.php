<?php
/**
 * Test per debug del calcolo SLC
 * Verifica i valori interpolati per TR=1462
 */

require_once 'includes/SeismicCalculator.php';

// Dati di test
$lat = 42.417406;
$lng = 14.165970;
$TR_target = 1462; // TR per SLC

echo "<h2>Debug Calcolo SLC - TR=$TR_target</h2>\n";
echo "<p>Coordinate: Lat=$lat, Lng=$lng</p>\n";

try {
    $calculator = new SeismicCalculator();
    
    // Test 1: Verifica i periodi di ritorno utilizzati per l'interpolazione
    echo "<h3>1. Periodi di Ritorno per Interpolazione</h3>\n";
    $TR_values = [30, 50, 72, 101, 140, 201, 475, 975, 2475];
    
    $lower_TR = null;
    $upper_TR = null;
    
    for ($i = 0; $i < count($TR_values); $i++) {
        if ($TR_values[$i] > $TR_target) {
            if ($i > 0) {
                $lower_TR = $TR_values[$i-1];
                $upper_TR = $TR_values[$i];
            } else {
                $lower_TR = $TR_values[0];
                $upper_TR = $TR_values[1];
            }
            break;
        }
    }
    
    if ($lower_TR === null) {
        $lower_TR = $TR_values[count($TR_values)-2];
        $upper_TR = $TR_values[count($TR_values)-1];
    }
    
    echo "<p>TR target: $TR_target</p>\n";
    echo "<p>TR lower: $lower_TR</p>\n";
    echo "<p>TR upper: $upper_TR</p>\n";
    
    // Test 2: Recupera i dati grezzi dal database
    echo "<h3>2. Dati Grezzi dal Database</h3>\n";
    
    $conn = new PDO("mysql:host=localhost;dbname=asdp_db;charset=utf8", "root", "");
    $conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    $stmt = $conn->prepare("
        SELECT 
            ag_{$lower_TR}/9.81 as ag_lower,
            fo_{$lower_TR} as fo_lower, 
            tc_{$lower_TR} as tc_lower,
            ag_{$upper_TR}/9.81 as ag_upper,
            fo_{$upper_TR} as fo_upper, 
            tc_{$upper_TR} as tc_upper,
            latitude, longitude,
            POW(latitude - ?, 2) + POW(longitude - ?, 2) as distance
        FROM seismic_grid_points
        WHERE latitude BETWEEN ? - 0.5 AND ? + 0.5 
        AND longitude BETWEEN ? - 0.5 AND ? + 0.5 
        ORDER BY POW(latitude - ?, 2) + POW(longitude - ?, 2)
        LIMIT 4
    ");
    
    $stmt->execute([$lat, $lng, $lat, $lat, $lng, $lng, $lat, $lng]);
    $points = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<table border='1' style='border-collapse: collapse;'>\n";
    echo "<tr><th>Punto</th><th>Lat</th><th>Lng</th><th>Distanza</th><th>ag_$lower_TR</th><th>fo_$lower_TR</th><th>tc_$lower_TR</th><th>ag_$upper_TR</th><th>fo_$upper_TR</th><th>tc_$upper_TR</th></tr>\n";
    
    foreach ($points as $i => $point) {
        echo "<tr>";
        echo "<td>P" . ($i+1) . "</td>";
        echo "<td>" . number_format($point['latitude'], 6) . "</td>";
        echo "<td>" . number_format($point['longitude'], 6) . "</td>";
        echo "<td>" . number_format($point['distance'], 8) . "</td>";
        echo "<td>" . number_format($point['ag_lower'], 4) . "</td>";
        echo "<td>" . number_format($point['fo_lower'], 3) . "</td>";
        echo "<td>" . number_format($point['tc_lower'], 3) . "</td>";
        echo "<td>" . number_format($point['ag_upper'], 4) . "</td>";
        echo "<td>" . number_format($point['fo_upper'], 3) . "</td>";
        echo "<td>" . number_format($point['tc_upper'], 3) . "</td>";
        echo "</tr>\n";
    }
    echo "</table>\n";
    
    // Test 3: Interpolazione spaziale per ogni periodo
    echo "<h3>3. Interpolazione Spaziale</h3>\n";
    
    // Ordina i punti
    usort($points, function($a, $b) {
        if ($a['latitude'] != $b['latitude']) {
            return $a['latitude'] < $b['latitude'] ? -1 : 1;
        }
        return $a['longitude'] < $b['longitude'] ? -1 : 1;
    });
    
    // Calcola coefficienti di interpolazione
    $x = ($lng - $points[0]['longitude']) / ($points[1]['longitude'] - $points[0]['longitude']);
    $y = ($lat - $points[0]['latitude']) / ($points[2]['latitude'] - $points[0]['latitude']);
    
    echo "<p>Coefficienti interpolazione: x=" . number_format($x, 6) . ", y=" . number_format($y, 6) . "</p>\n";
    
    // Interpola per TR lower
    $ag_lower_interp = $points[0]['ag_lower'] * (1-$x) * (1-$y) +
                       $points[1]['ag_lower'] * $x * (1-$y) +
                       $points[2]['ag_lower'] * (1-$x) * $y +
                       $points[3]['ag_lower'] * $x * $y;
    
    $fo_lower_interp = $points[0]['fo_lower'] * (1-$x) * (1-$y) +
                       $points[1]['fo_lower'] * $x * (1-$y) +
                       $points[2]['fo_lower'] * (1-$x) * $y +
                       $points[3]['fo_lower'] * $x * $y;
    
    $tc_lower_interp = $points[0]['tc_lower'] * (1-$x) * (1-$y) +
                       $points[1]['tc_lower'] * $x * (1-$y) +
                       $points[2]['tc_lower'] * (1-$x) * $y +
                       $points[3]['tc_lower'] * $x * $y;
    
    // Interpola per TR upper
    $ag_upper_interp = $points[0]['ag_upper'] * (1-$x) * (1-$y) +
                       $points[1]['ag_upper'] * $x * (1-$y) +
                       $points[2]['ag_upper'] * (1-$x) * $y +
                       $points[3]['ag_upper'] * $x * $y;
    
    $fo_upper_interp = $points[0]['fo_upper'] * (1-$x) * (1-$y) +
                       $points[1]['fo_upper'] * $x * (1-$y) +
                       $points[2]['fo_upper'] * (1-$x) * $y +
                       $points[3]['fo_upper'] * $x * $y;
    
    $tc_upper_interp = $points[0]['tc_upper'] * (1-$x) * (1-$y) +
                       $points[1]['tc_upper'] * $x * (1-$y) +
                       $points[2]['tc_upper'] * (1-$x) * $y +
                       $points[3]['tc_upper'] * $x * $y;
    
    echo "<table border='1' style='border-collapse: collapse;'>\n";
    echo "<tr><th>Parametro</th><th>TR=$lower_TR (interpolato)</th><th>TR=$upper_TR (interpolato)</th></tr>\n";
    echo "<tr><td>ag</td><td>" . number_format($ag_lower_interp, 4) . "</td><td>" . number_format($ag_upper_interp, 4) . "</td></tr>\n";
    echo "<tr><td>F0</td><td>" . number_format($fo_lower_interp, 3) . "</td><td>" . number_format($fo_upper_interp, 3) . "</td></tr>\n";
    echo "<tr><td>Tc*</td><td>" . number_format($tc_lower_interp, 3) . "</td><td>" . number_format($tc_upper_interp, 3) . "</td></tr>\n";
    echo "</table>\n";
    
    // Test 4: Interpolazione temporale finale
    echo "<h3>4. Interpolazione Temporale (TR=$TR_target)</h3>\n";
    
    $x_time = (log($TR_target) - log($lower_TR)) / (log($upper_TR) - log($lower_TR));
    echo "<p>Coefficiente interpolazione temporale: x_time=" . number_format($x_time, 6) . "</p>\n";
    
    $ag_final = $ag_lower_interp + $x_time * ($ag_upper_interp - $ag_lower_interp);
    $fo_final = $fo_lower_interp + $x_time * ($fo_upper_interp - $fo_lower_interp);
    $tc_final = $tc_lower_interp + $x_time * ($tc_upper_interp - $tc_lower_interp);
    
    echo "<table border='1' style='border-collapse: collapse;'>\n";
    echo "<tr><th>Parametro</th><th>Valore Calcolato</th><th>Valore Atteso</th><th>Differenza</th></tr>\n";
    echo "<tr><td>ag</td><td>" . number_format($ag_final, 4) . "</td><td>0.232</td><td>" . number_format(abs($ag_final - 0.232), 4) . "</td></tr>\n";
    echo "<tr><td>F0</td><td>" . number_format($fo_final, 3) . "</td><td>2.504</td><td>" . number_format(abs($fo_final - 2.504), 3) . "</td></tr>\n";
    echo "<tr><td>Tc*</td><td>" . number_format($tc_final, 3) . "</td><td>0.362</td><td>" . number_format(abs($tc_final - 0.362), 3) . "</td></tr>\n";
    echo "</table>\n";
    
    // Test 5: Verifica con il metodo della classe
    echo "<h3>5. Verifica con SeismicCalculator</h3>\n";
    $result = $calculator->interpolateForTR($lat, $lng, $TR_target);
    
    echo "<table border='1' style='border-collapse: collapse;'>\n";
    echo "<tr><th>Parametro</th><th>Metodo Classe</th><th>Calcolo Manuale</th><th>Differenza</th></tr>\n";
    echo "<tr><td>ag</td><td>" . number_format($result['ag'], 4) . "</td><td>" . number_format($ag_final, 4) . "</td><td>" . number_format(abs($result['ag'] - $ag_final), 6) . "</td></tr>\n";
    echo "<tr><td>F0</td><td>" . number_format($result['F0'], 3) . "</td><td>" . number_format($fo_final, 3) . "</td><td>" . number_format(abs($result['F0'] - $fo_final), 6) . "</td></tr>\n";
    echo "<tr><td>Tc*</td><td>" . number_format($result['TC*'], 3) . "</td><td>" . number_format($tc_final, 3) . "</td><td>" . number_format(abs($result['TC*'] - $tc_final), 6) . "</td></tr>\n";
    echo "</table>\n";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>Errore: " . $e->getMessage() . "</p>\n";
}
?>
