<?php
/**
 * Script per importare tutti i dati dell'Italia dal file Excel
 * File: docs/analisi_normative/10408-pdf16.xlsx
 */

require_once 'includes/db_config.php';
require_once 'includes/SimpleXLSX.php';

use <PERSON><PERSON><PERSON>\SimpleXLSX;

echo "=== IMPORTAZIONE COMPLETA DATI ITALIA ===\n\n";

$excel_file = 'docs/analisi_normative/10408-pdf16.xlsx';

try {
    // Apri il file Excel
    echo "1. Apertura file Excel...\n";
    $xlsx = SimpleXLSX::parse($excel_file);
    
    if (!$xlsx) {
        die("ERRORE: " . SimpleXLSX::parseError() . "\n");
    }
    
    $sheet_names = $xlsx->sheetNames();
    echo "   ✓ File aperto: " . count($sheet_names) . " fogli\n\n";
    
    // Connessione database
    echo "2. Connessione database...\n";
    $conn = new PDO("mysql:host=localhost;dbname=asdp_db;charset=utf8", "root", "");
    $conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "   ✓ Connesso al database\n\n";
    
    // Backup completo
    echo "3. Creazione backup completo...\n";
    $backup_table = "seismic_grid_points_backup_italy_" . date('Y_m_d_H_i_s');
    $conn->exec("CREATE TABLE $backup_table AS SELECT * FROM seismic_grid_points");
    echo "   ✓ Backup creato: $backup_table\n\n";
    
    // Mappa dei fogli ai parametri
    // Assumiamo che i fogli siano organizzati per TR
    $tr_mapping = [
        // I primi 9 fogli dovrebbero essere per i 9 TR
        0 => 30,    // Table 1
        1 => 50,    // Table 2  
        2 => 72,    // Table 3
        3 => 101,   // Table 4
        4 => 140,   // Table 5
        5 => 201,   // Table 6
        6 => 475,   // Table 7
        7 => 975,   // Table 8
        8 => 2475   // Table 9
    ];
    
    echo "4. Importazione dati per ogni TR...\n";
    
    foreach ($tr_mapping as $sheet_index => $tr) {
        echo "   Importazione TR=$tr (Foglio {$sheet_names[$sheet_index]})...\n";
        
        $rows = $xlsx->rows($sheet_index);
        if (!$rows) {
            echo "     ✗ Errore lettura foglio\n";
            continue;
        }
        
        $row_count = 0;
        $updated_count = 0;
        $header_found = false;
        $lat_col = -1;
        $lng_col = -1;
        $ag_col = -1;
        $fo_col = -1;
        $tc_col = -1;
        
        // Prepara statement di update
        $stmt = $conn->prepare("
            UPDATE seismic_grid_points 
            SET ag_$tr = ?, fo_$tr = ?, tc_$tr = ?
            WHERE ABS(latitude - ?) < 0.001 AND ABS(longitude - ?) < 0.001
        ");
        
        // Prepara statement di insert per nuovi punti
        $insert_stmt = $conn->prepare("
            INSERT IGNORE INTO seismic_grid_points 
            (latitude, longitude, ag_$tr, fo_$tr, tc_$tr) 
            VALUES (?, ?, ?, ?, ?)
        ");
        
        foreach ($rows as $row) {
            $row_count++;
            
            // Cerca l'header nelle prime 10 righe
            if (!$header_found && $row_count <= 10) {
                for ($i = 0; $i < count($row); $i++) {
                    $cell = strtolower(trim($row[$i]));
                    if (strpos($cell, 'lat') !== false) {
                        $lat_col = $i;
                    } elseif (strpos($cell, 'lon') !== false) {
                        $lng_col = $i;
                    } elseif (strpos($cell, 'ag') !== false && $ag_col == -1) {
                        $ag_col = $i;
                    } elseif (strpos($cell, 'fo') !== false && $fo_col == -1) {
                        $fo_col = $i;
                    } elseif (strpos($cell, 'tc') !== false && $tc_col == -1) {
                        $tc_col = $i;
                    }
                }
                
                if ($lat_col >= 0 && $lng_col >= 0) {
                    $header_found = true;
                    echo "     Header trovato alla riga $row_count\n";
                    echo "     Colonne: lat=$lat_col, lng=$lng_col, ag=$ag_col, fo=$fo_col, tc=$tc_col\n";
                    continue;
                }
            }
            
            // Se non troviamo header, proviamo formato standard
            if (!$header_found && $row_count > 10) {
                // Analizza la prima riga di dati per determinare le colonne
                for ($i = 0; $i < count($row); $i++) {
                    $val = floatval($row[$i]);
                    // Latitudine Italia: 35-48
                    if ($val >= 35 && $val <= 48 && $lat_col == -1) {
                        $lat_col = $i;
                    }
                    // Longitudine Italia: 6-19  
                    elseif ($val >= 6 && $val <= 19 && $lng_col == -1) {
                        $lng_col = $i;
                    }
                }
                
                // Se troviamo lat/lng, assumiamo che ag, fo, tc siano nelle ultime colonne
                if ($lat_col >= 0 && $lng_col >= 0) {
                    $total_cols = count($row);
                    $tc_col = $total_cols - 1;  // Ultima colonna
                    $fo_col = $total_cols - 2;  // Penultima colonna
                    $ag_col = $total_cols - 3;  // Terzultima colonna
                    
                    $header_found = true;
                    echo "     Formato auto-rilevato alla riga $row_count\n";
                    echo "     Colonne: lat=$lat_col, lng=$lng_col, ag=$ag_col, fo=$fo_col, tc=$tc_col\n";
                }
            }
            
            if (!$header_found) {
                continue;
            }
            
            // Verifica che la riga abbia abbastanza colonne
            $max_col = max($lat_col, $lng_col, $ag_col, $fo_col, $tc_col);
            if (count($row) <= $max_col || $max_col < 0) {
                continue;
            }
            
            // Estrai i valori
            $lat = floatval($row[$lat_col]);
            $lng = floatval($row[$lng_col]);
            $ag = floatval($row[$ag_col]);
            $fo = floatval($row[$fo_col]);
            $tc = floatval($row[$tc_col]);
            
            // Verifica validità coordinate Italia
            if ($lat < 35 || $lat > 48 || $lng < 6 || $lng > 19) {
                continue;
            }
            
            // Verifica validità dati sismici
            if ($ag <= 0 || $fo <= 0 || $tc <= 0) {
                continue;
            }
            
            // Converti ag da g a m/s² se necessario
            if ($ag < 1.0) {
                $ag = $ag * 9.81;
            }
            
            // Prova prima l'update
            $stmt->execute([$ag, $fo, $tc, $lat, $lng]);
            $updated = $stmt->rowCount();
            
            if ($updated > 0) {
                $updated_count++;
            } else {
                // Se non ha aggiornato, prova l'insert
                $insert_stmt->execute([$lat, $lng, $ag, $fo, $tc]);
                if ($insert_stmt->rowCount() > 0) {
                    $updated_count++;
                }
            }
            
            if ($row_count % 1000 == 0) {
                echo "     Processate $row_count righe, aggiornate $updated_count...\n";
            }
        }
        
        echo "     ✓ TR=$tr: $row_count righe processate, $updated_count punti aggiornati\n\n";
    }
    
    echo "5. Verifica risultati finali...\n";
    
    // Statistiche generali
    $stmt = $conn->query("SELECT COUNT(*) as total FROM seismic_grid_points");
    $total_points = $stmt->fetch(PDO::FETCH_ASSOC)['total'];
    echo "   Punti totali nel database: $total_points\n";
    
    // Verifica tc_975
    $stmt = $conn->query("
        SELECT 
            COUNT(CASE WHEN tc_975 = 0 THEN 1 END) as zero_count,
            COUNT(CASE WHEN tc_975 > 0 THEN 1 END) as valid_count,
            MIN(tc_975) as min_val,
            MAX(tc_975) as max_val,
            AVG(tc_975) as avg_val
        FROM seismic_grid_points
    ");
    
    $stats975 = $stmt->fetch(PDO::FETCH_ASSOC);
    echo "   TC_975 - Zero: {$stats975['zero_count']}, Validi: {$stats975['valid_count']}\n";
    echo "   TC_975 - Range: " . number_format($stats975['min_val'], 3) . " - " . number_format($stats975['max_val'], 3) . " (media: " . number_format($stats975['avg_val'], 3) . ")\n";
    
    // Verifica tc_2475
    $stmt = $conn->query("
        SELECT 
            COUNT(CASE WHEN tc_2475 > 1.0 THEN 1 END) as anomalous_count,
            COUNT(CASE WHEN tc_2475 <= 1.0 AND tc_2475 > 0 THEN 1 END) as normal_count,
            MIN(tc_2475) as min_val,
            MAX(tc_2475) as max_val,
            AVG(tc_2475) as avg_val
        FROM seismic_grid_points
    ");
    
    $stats2475 = $stmt->fetch(PDO::FETCH_ASSOC);
    echo "   TC_2475 - Normali (≤1.0): {$stats2475['normal_count']}, Anomali (>1.0): {$stats2475['anomalous_count']}\n";
    echo "   TC_2475 - Range: " . number_format($stats2475['min_val'], 3) . " - " . number_format($stats2475['max_val'], 3) . " (media: " . number_format($stats2475['avg_val'], 3) . ")\n\n";
    
    // Test finale con coordinate di riferimento
    echo "6. Test finale con coordinate di riferimento...\n";
    
    require_once 'includes/SeismicCalculator.php';
    $calculator = new SeismicCalculator();
    
    $lat = 42.417406;
    $lng = 14.165970;
    
    $reference_values = [
        'SLO' => ['TR' => 45, 'ag' => 0.058, 'F0' => 2.469, 'TC' => 0.302],
        'SLD' => ['TR' => 75, 'ag' => 0.072, 'F0' => 2.461, 'TC' => 0.323],
        'SLV' => ['TR' => 712, 'ag' => 0.180, 'F0' => 2.481, 'TC' => 0.358],
        'SLC' => ['TR' => 1462, 'ag' => 0.232, 'F0' => 2.504, 'TC' => 0.362]
    ];
    
    $all_passed = true;
    
    foreach ($reference_values as $state => $ref) {
        echo "   $state (TR={$ref['TR']}):\n";
        $result = $calculator->interpolateForTR($lat, $lng, $ref['TR']);
        
        foreach (['ag', 'F0', 'TC'] as $param) {
            $param_key = ($param == 'TC') ? 'TC*' : $param;
            $expected = $ref[$param];
            $calculated = $result[$param_key];
            $error = abs($calculated - $expected) / $expected * 100;
            
            $status = $error < 10 ? "✓" : "✗";
            if ($error >= 10) $all_passed = false;
            
            echo "     $param: " . number_format($calculated, 4) . " (atteso: " . number_format($expected, 4) . ", errore: " . number_format($error, 1) . "%) $status\n";
        }
        echo "\n";
    }
    
    if ($all_passed) {
        echo "🎉 TUTTI I TEST SUPERATI! Importazione completata con successo.\n";
    } else {
        echo "⚠ Alcuni test non superati. Potrebbero essere necessarie ulteriori correzioni.\n";
    }
    
    echo "\n=== IMPORTAZIONE ITALIA COMPLETATA ===\n";
    
} catch (Exception $e) {
    echo "ERRORE: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}
?>
