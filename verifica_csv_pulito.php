<?php
/**
 * Script per verificare la conformità del CSV pulito e estrarre dati ID 10007
 */

echo "=== VERIFICA CSV PULITO E ESTRAZIONE ID 10007 ===\n\n";

$csv_file = 'docs/analisi_normative/conversione_pulito.CSV';

try {
    // Verifica esistenza file
    if (!file_exists($csv_file)) {
        die("ERRORE: File $csv_file non trovato\n");
    }
    
    echo "1. Informazioni file CSV pulito...\n";
    echo "   File: $csv_file\n";
    echo "   Dimensione: " . number_format(filesize($csv_file)) . " bytes\n\n";
    
    // Apri file CSV
    $handle = fopen($csv_file, 'r');
    if (!$handle) {
        die("ERRORE: Impossibile aprire il file CSV\n");
    }
    
    echo "2. Analisi struttura generale...\n";
    
    $total_rows = 0;
    $column_counts = [];
    $id_10007_found = false;
    $id_10007_data = null;
    $sample_rows = [];
    
    // Analizza tutte le righe
    while (($row = fgetcsv($handle, 0, ';')) !== FALSE) {
        $total_rows++;
        
        // Conta colonne per ogni riga
        $col_count = count($row);
        if (!isset($column_counts[$col_count])) {
            $column_counts[$col_count] = 0;
        }
        $column_counts[$col_count]++;
        
        // Cerca ID 10007
        if (isset($row[0]) && trim($row[0]) == '10007') {
            $id_10007_found = true;
            $id_10007_data = $row;
            echo "   ✓ ID 10007 trovato alla riga $total_rows\n";
        }
        
        // Salva prime 5 righe come esempio
        if ($total_rows <= 5) {
            $sample_rows[] = $row;
        }
        
        if ($total_rows % 2000 == 0) {
            echo "   Analizzate $total_rows righe...\n";
        }
    }
    
    fclose($handle);
    
    echo "\n   Righe totali: $total_rows\n";
    echo "   Distribuzione colonne:\n";
    foreach ($column_counts as $cols => $count) {
        $percentage = round(($count / $total_rows) * 100, 1);
        echo "     $cols colonne: $count righe ($percentage%)\n";
    }
    
    // Verifica conformità
    echo "\n3. Verifica conformità...\n";
    
    $expected_columns = 30;
    $conformity_check = true;
    
    if (isset($column_counts[$expected_columns])) {
        $correct_rows = $column_counts[$expected_columns];
        $correct_percentage = round(($correct_rows / $total_rows) * 100, 1);
        echo "   ✓ Righe con $expected_columns colonne: $correct_rows ($correct_percentage%)\n";
        
        if ($correct_percentage >= 99) {
            echo "   ✅ CONFORMITÀ ECCELLENTE: >99% delle righe corrette\n";
        } elseif ($correct_percentage >= 95) {
            echo "   ✅ CONFORMITÀ BUONA: >95% delle righe corrette\n";
        } else {
            echo "   ⚠ CONFORMITÀ PARZIALE: <95% delle righe corrette\n";
            $conformity_check = false;
        }
    } else {
        echo "   ❌ NESSUNA RIGA con $expected_columns colonne trovata\n";
        $conformity_check = false;
    }
    
    // Mostra righe con numero di colonne diverso
    foreach ($column_counts as $cols => $count) {
        if ($cols != $expected_columns && $count > 0) {
            echo "   ⚠ $count righe con $cols colonne (anomalo)\n";
        }
    }
    
    // Analisi prime righe
    echo "\n4. Analisi prime righe (struttura)...\n";
    
    for ($i = 0; $i < min(3, count($sample_rows)); $i++) {
        $row = $sample_rows[$i];
        echo "   Riga " . ($i + 1) . " (" . count($row) . " colonne):\n";
        echo "     ID: " . trim($row[0]) . "\n";
        echo "     LON: " . trim($row[1]) . "\n";
        echo "     LAT: " . trim($row[2]) . "\n";
        echo "     TR30 (AG,FO,TC): " . trim($row[3]) . ", " . trim($row[4]) . ", " . trim($row[5]) . "\n";
        echo "     TR975 (AG,FO,TC): " . trim($row[24]) . ", " . trim($row[25]) . ", " . trim($row[26]) . "\n";
        echo "     TR2475 (AG,FO,TC): " . trim($row[27]) . ", " . trim($row[28]) . ", " . trim($row[29]) . "\n";
        echo "\n";
    }
    
    // Estrazione e analisi ID 10007
    echo "5. Analisi dettagliata ID 10007...\n";
    
    if ($id_10007_found && $id_10007_data) {
        echo "   ✅ ID 10007 TROVATO!\n\n";
        
        $data = $id_10007_data;
        
        // Informazioni base
        echo "   📍 COORDINATE:\n";
        echo "     ID: " . trim($data[0]) . "\n";
        echo "     Longitudine: " . trim($data[1]) . "\n";
        echo "     Latitudine: " . trim($data[2]) . "\n";
        
        // Verifica coordinate Italia
        $lng = floatval(str_replace(',', '.', trim($data[1])));
        $lat = floatval(str_replace(',', '.', trim($data[2])));
        
        if ($lat >= 35 && $lat <= 48 && $lng >= 6 && $lng <= 19) {
            echo "     ✅ Coordinate valide per l'Italia\n";
        } else {
            echo "     ❌ Coordinate NON valide per l'Italia\n";
        }
        
        // Mappatura TR corretta per file pulito
        $tr_mapping = [
            30   => ['ag' => 3,  'fo' => 4,  'tc' => 5],
            50   => ['ag' => 6,  'fo' => 7,  'tc' => 8],
            72   => ['ag' => 9,  'fo' => 10, 'tc' => 11],
            101  => ['ag' => 12, 'fo' => 13, 'tc' => 14],
            140  => ['ag' => 15, 'fo' => 16, 'tc' => 17],
            201  => ['ag' => 18, 'fo' => 19, 'tc' => 20],
            475  => ['ag' => 21, 'fo' => 22, 'tc' => 23],
            975  => ['ag' => 24, 'fo' => 25, 'tc' => 26],
            2475 => ['ag' => 27, 'fo' => 28, 'tc' => 29]
        ];
        
        echo "\n   📊 PARAMETRI SISMICI PER TUTTI I TR:\n";
        echo "   " . str_repeat("-", 60) . "\n";
        echo "   TR    |    AG    |    FO    |    TC    | Validità\n";
        echo "   " . str_repeat("-", 60) . "\n";
        
        $all_valid = true;
        
        foreach ($tr_mapping as $tr => $cols) {
            $ag_str = trim($data[$cols['ag']]);
            $fo_str = trim($data[$cols['fo']]);
            $tc_str = trim($data[$cols['tc']]);
            
            $ag = floatval(str_replace(',', '.', $ag_str));
            $fo = floatval(str_replace(',', '.', $fo_str));
            $tc = floatval(str_replace(',', '.', $tc_str));
            
            $valid = ($ag > 0 && $fo > 0 && $tc > 0);
            $status = $valid ? "✅" : "❌";
            
            if (!$valid) $all_valid = false;
            
            printf("   %-5s | %8s | %8s | %8s | %s\n", 
                   $tr, $ag_str, $fo_str, $tc_str, $status);
        }
        
        echo "   " . str_repeat("-", 60) . "\n";
        
        if ($all_valid) {
            echo "   ✅ TUTTI I PARAMETRI VALIDI per ID 10007\n";
        } else {
            echo "   ❌ Alcuni parametri non validi per ID 10007\n";
        }
        
        // Analisi valori TC per anomalie
        echo "\n   🔍 ANALISI VALORI TC (controllo anomalie):\n";
        
        foreach ($tr_mapping as $tr => $cols) {
            $tc_str = trim($data[$cols['tc']]);
            $tc = floatval(str_replace(',', '.', $tc_str));
            
            if ($tr == 2475) {
                if ($tc <= 1.0) {
                    echo "     TR $tr TC: $tc_str ✅ (normale, ≤1.0)\n";
                } else {
                    echo "     TR $tr TC: $tc_str ❌ (anomalo, >1.0)\n";
                }
            } else {
                if ($tc > 0 && $tc <= 1.0) {
                    echo "     TR $tr TC: $tc_str ✅ (normale)\n";
                } else {
                    echo "     TR $tr TC: $tc_str ⚠ (verifica necessaria)\n";
                }
            }
        }
        
        // Confronto con riga originale (se disponibile)
        echo "\n   📋 RIGA COMPLETA ID 10007:\n";
        echo "   " . implode(';', $data) . "\n";
        
    } else {
        echo "   ❌ ID 10007 NON TROVATO nel file\n";
    }
    
    // Riepilogo finale
    echo "\n6. Riepilogo verifica...\n";
    echo "   " . str_repeat("=", 50) . "\n";
    
    if ($conformity_check) {
        echo "   ✅ STRUTTURA CSV: Conforme (30 colonne)\n";
    } else {
        echo "   ❌ STRUTTURA CSV: Non conforme\n";
    }
    
    if ($id_10007_found) {
        echo "   ✅ ID 10007: Trovato e analizzato\n";
    } else {
        echo "   ❌ ID 10007: Non trovato\n";
    }
    
    echo "   📊 STATISTICHE:\n";
    echo "     - Righe totali: $total_rows\n";
    echo "     - Colonne attese: $expected_columns\n";
    echo "     - File pulito: " . ($conformity_check ? "SÌ" : "NO") . "\n";
    
    if ($conformity_check && $id_10007_found) {
        echo "\n   🎉 VERIFICA COMPLETATA CON SUCCESSO!\n";
        echo "   ✅ File CSV pulito è conforme e pronto per importazione\n";
    } else {
        echo "\n   ⚠ VERIFICA COMPLETATA CON PROBLEMI\n";
        echo "   🔧 Potrebbero essere necessarie correzioni\n";
    }
    
    echo "\n=== VERIFICA COMPLETATA ===\n";
    
} catch (Exception $e) {
    echo "ERRORE: " . $e->getMessage() . "\n";
    if (isset($handle)) fclose($handle);
}
?>
