<?php
/**
 * Script per scaricare e installare SimpleXLSX
 */

echo "=== INSTALLAZIONE SIMPLEXLSX ===\n\n";

// URL del file SimpleXLSX
$simplexlsx_url = 'https://github.com/shuchkin/simplexlsx/raw/master/src/SimpleXLSX.php';
$target_file = 'includes/SimpleXLSX.php';

echo "1. Download SimpleXLSX...\n";
echo "   URL: $simplexlsx_url\n";
echo "   Target: $target_file\n";

// Verifica se curl è disponibile
if (!function_exists('curl_init')) {
    echo "   ✗ CURL non disponibile\n";
    echo "   Prova a scaricare manualmente il file da:\n";
    echo "   $simplexlsx_url\n";
    echo "   E salvalo come: $target_file\n";
    exit(1);
}

// Download del file
$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $simplexlsx_url);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36');

$content = curl_exec($ch);
$http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
curl_close($ch);

if ($http_code == 200 && $content) {
    file_put_contents($target_file, $content);
    echo "   ✓ SimpleXLSX scaricato con successo\n";
    echo "   Dimensione: " . number_format(strlen($content)) . " bytes\n\n";
    
    // Test della libreria
    echo "2. Test della libreria...\n";
    
    if (file_exists($target_file)) {
        require_once $target_file;
        
        if (class_exists('SimpleXLSX')) {
            echo "   ✓ SimpleXLSX caricato correttamente\n";
            
            // Test con il file Excel
            $excel_file = 'docs/analisi_normative/10408-pdf16.xlsx';
            if (file_exists($excel_file)) {
                echo "   ✓ File Excel trovato: $excel_file\n";
                
                try {
                    $xlsx = SimpleXLSX::parse($excel_file);
                    if ($xlsx) {
                        echo "   ✓ File Excel aperto con successo\n";
                        echo "   Fogli disponibili: " . count($xlsx->sheetNames()) . "\n";
                        
                        foreach ($xlsx->sheetNames() as $i => $name) {
                            echo "     Foglio $i: $name\n";
                        }
                        
                        echo "\n=== INSTALLAZIONE COMPLETATA ===\n";
                        echo "Ora puoi eseguire: php import_excel_data_direct.php\n";
                        
                    } else {
                        echo "   ✗ Errore apertura file Excel: " . SimpleXLSX::parseError() . "\n";
                    }
                } catch (Exception $e) {
                    echo "   ✗ Errore test: " . $e->getMessage() . "\n";
                }
            } else {
                echo "   ✗ File Excel non trovato\n";
            }
        } else {
            echo "   ✗ Classe SimpleXLSX non trovata\n";
        }
    } else {
        echo "   ✗ File non salvato correttamente\n";
    }
} else {
    echo "   ✗ Errore download (HTTP $http_code)\n";
    echo "   Prova a scaricare manualmente\n";
}
?>
