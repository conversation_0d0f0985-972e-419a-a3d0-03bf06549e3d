<?php
/**
 * Script per analizzare la struttura del file Excel
 */

require_once 'includes/SimpleXLSX.php';

use <PERSON><PERSON><PERSON>\SimpleXLSX;

echo "=== ANALISI STRUTTURA FILE EXCEL ===\n\n";

$excel_file = 'docs/analisi_normative/10408-pdf16.xlsx';

try {
    $xlsx = SimpleXLSX::parse($excel_file);
    
    if (!$xlsx) {
        die("ERRORE: " . SimpleXLSX::parseError() . "\n");
    }
    
    $sheet_names = $xlsx->sheetNames();
    echo "Fogli totali: " . count($sheet_names) . "\n\n";
    
    // Analizza i primi 10 fogli per capire la struttura
    echo "ANALISI PRIMI 10 FOGLI:\n";
    echo str_repeat("=", 80) . "\n";
    
    for ($i = 0; $i < min(10, count($sheet_names)); $i++) {
        echo "FOGLIO $i: {$sheet_names[$i]}\n";
        echo str_repeat("-", 40) . "\n";
        
        $rows = $xlsx->rows($i);
        if (!$rows) {
            echo "   Errore lettura foglio\n\n";
            continue;
        }
        
        $row_count = 0;
        $sample_rows = [];
        
        foreach ($rows as $row) {
            $row_count++;
            if ($row_count <= 5) {
                $sample_rows[] = $row;
            }
            if ($row_count > 100) break; // Limita per performance
        }
        
        echo "   Righe totali: $row_count\n";
        echo "   Colonne: " . (count($sample_rows) > 0 ? count($sample_rows[0]) : 0) . "\n";
        
        echo "   Prime 5 righe:\n";
        foreach ($sample_rows as $r => $row) {
            echo "     Riga " . ($r+1) . ": ";
            $preview = [];
            for ($c = 0; $c < min(10, count($row)); $c++) {
                $cell = trim($row[$c]);
                if (strlen($cell) > 15) {
                    $cell = substr($cell, 0, 12) . "...";
                }
                $preview[] = $cell;
            }
            echo implode(" | ", $preview) . "\n";
        }
        echo "\n";
    }
    
    // Cerca fogli che potrebbero contenere dati TC_975
    echo "RICERCA FOGLI CON DATI TC_975:\n";
    echo str_repeat("=", 80) . "\n";
    
    $tc975_sheets = [];
    
    for ($i = 0; $i < count($sheet_names); $i++) {
        $sheet_name = strtolower($sheet_names[$i]);
        
        // Cerca fogli che potrebbero contenere TC_975
        if (strpos($sheet_name, '975') !== false || 
            strpos($sheet_name, 'tc') !== false ||
            strpos($sheet_name, 'table 8') !== false) {
            
            echo "Foglio candidato $i: {$sheet_names[$i]}\n";
            
            $rows = $xlsx->rows($i);
            if ($rows) {
                $row_count = 0;
                foreach ($rows as $row) {
                    $row_count++;
                    if ($row_count > 10) break;
                }
                echo "   Righe: $row_count\n";
                
                // Mostra header
                $first_row = null;
                foreach ($rows as $row) {
                    $first_row = $row;
                    break;
                }
                
                if ($first_row) {
                    echo "   Header: ";
                    $header_preview = [];
                    for ($c = 0; $c < min(10, count($first_row)); $c++) {
                        $cell = trim($first_row[$c]);
                        if (strlen($cell) > 10) {
                            $cell = substr($cell, 0, 7) . "...";
                        }
                        $header_preview[] = $cell;
                    }
                    echo implode(" | ", $header_preview) . "\n";
                }
            }
            echo "\n";
            
            $tc975_sheets[] = $i;
        }
    }
    
    // Analisi specifica del foglio Table 8 (dovrebbe essere TC_975)
    echo "ANALISI DETTAGLIATA FOGLIO 'Table 8' (TC_975):\n";
    echo str_repeat("=", 80) . "\n";
    
    $table8_index = array_search('Table 8', $sheet_names);
    if ($table8_index !== false) {
        echo "Foglio 'Table 8' trovato all'indice: $table8_index\n";
        
        $rows = $xlsx->rows($table8_index);
        if ($rows) {
            $row_count = 0;
            $data_rows = 0;
            $lat_values = [];
            $lng_values = [];
            $tc_values = [];
            
            foreach ($rows as $row) {
                $row_count++;
                
                // Cerca dati numerici che potrebbero essere coordinate
                if (count($row) >= 30) { // Assumiamo che ci siano molte colonne
                    $lat = floatval($row[2]); // Colonna lat
                    $lng = floatval($row[1]); // Colonna lng
                    $tc = floatval($row[29]); // Colonna TC (ultima)
                    
                    if ($lat > 35 && $lat < 48 && $lng > 6 && $lng < 19) { // Range Italia
                        $data_rows++;
                        $lat_values[] = $lat;
                        $lng_values[] = $lng;
                        $tc_values[] = $tc;
                        
                        if ($data_rows <= 5) {
                            echo "   Riga $row_count: Lat=" . number_format($lat, 6) . ", Lng=" . number_format($lng, 6) . ", TC=" . number_format($tc, 3) . "\n";
                        }
                    }
                }
                
                if ($row_count > 100) break; // Limita per performance
            }
            
            echo "   Righe totali: $row_count\n";
            echo "   Righe con dati validi: $data_rows\n";
            
            if (count($lat_values) > 0) {
                echo "   Range Latitudine: " . number_format(min($lat_values), 6) . " - " . number_format(max($lat_values), 6) . "\n";
                echo "   Range Longitudine: " . number_format(min($lng_values), 6) . " - " . number_format(max($lng_values), 6) . "\n";
                echo "   Range TC: " . number_format(min($tc_values), 3) . " - " . number_format(max($tc_values), 3) . "\n";
            }
        }
    } else {
        echo "Foglio 'Table 8' non trovato!\n";
    }
    
} catch (Exception $e) {
    echo "ERRORE: " . $e->getMessage() . "\n";
}
?>
