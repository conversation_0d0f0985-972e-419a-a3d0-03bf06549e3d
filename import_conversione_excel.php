<?php
/**
 * <PERSON>ript per importare i dati dal file conversione.xlsx
 * Questo file dovrebbe contenere tutti i dati dell'Italia in una singola tabella
 */

require_once 'includes/db_config.php';
require_once 'includes/SimpleXLSX.php';

use <PERSON><PERSON><PERSON>\SimpleXLSX;

echo "=== IMPORTAZIONE DA CONVERSIONE.XLSX ===\n\n";

$excel_file = 'docs/analisi_normative/conversione.xlsx';

try {
    // Apri il file Excel
    echo "1. Apertura file Excel convertito...\n";
    $xlsx = SimpleXLSX::parse($excel_file);
    
    if (!$xlsx) {
        die("ERRORE: " . SimpleXLSX::parseError() . "\n");
    }
    
    $sheet_names = $xlsx->sheetNames();
    echo "   ✓ File aperto: " . count($sheet_names) . " fogli\n";
    echo "   Fogli disponibili: " . implode(', ', $sheet_names) . "\n\n";
    
    // Connessione database
    echo "2. Connessione database...\n";
    $conn = new PDO("mysql:host=localhost;dbname=asdp_db;charset=utf8", "root", "");
    $conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "   ✓ Connesso al database\n\n";
    
    // Backup completo
    echo "3. Creazione backup completo...\n";
    $backup_table = "seismic_grid_points_backup_conversione_" . date('Y_m_d_H_i_s');
    $conn->exec("CREATE TABLE $backup_table AS SELECT * FROM seismic_grid_points");
    echo "   ✓ Backup creato: $backup_table\n\n";
    
    // Analizza il primo foglio (dovrebbe contenere tutti i dati)
    echo "4. Analisi struttura dati...\n";
    
    $rows = $xlsx->rows(0);
    if (!$rows) {
        die("ERRORE: Impossibile leggere il foglio\n");
    }
    
    $row_count = 0;
    $header_row = null;
    $header_found = false;
    $data_start_row = 0;
    
    // Cerca l'header e analizza la struttura
    foreach ($rows as $row) {
        $row_count++;
        
        if ($row_count > 50) break; // Limita l'analisi alle prime 50 righe
        
        // Cerca una riga che contenga le intestazioni delle colonne
        if (!$header_found && count($row) > 10) {
            $has_lat = false;
            $has_lng = false;
            $has_ag = false;
            $has_tc = false;
            
            for ($i = 0; $i < count($row); $i++) {
                $cell = strtolower(trim($row[$i]));
                if (strpos($cell, 'lat') !== false || strpos($cell, 'latitude') !== false) {
                    $has_lat = true;
                } elseif (strpos($cell, 'lon') !== false || strpos($cell, 'longitude') !== false) {
                    $has_lng = true;
                } elseif (strpos($cell, 'ag') !== false) {
                    $has_ag = true;
                } elseif (strpos($cell, 'tc') !== false) {
                    $has_tc = true;
                }
            }
            
            if ($has_lat && $has_lng && ($has_ag || $has_tc)) {
                $header_row = $row;
                $header_found = true;
                $data_start_row = $row_count;
                echo "   ✓ Header trovato alla riga $row_count\n";
                echo "   Colonne: " . implode(' | ', array_slice($row, 0, 15)) . "...\n";
                break;
            }
        }
        
        // Se non troviamo header, verifica se ci sono dati numerici che sembrano coordinate
        if (!$header_found && count($row) > 10) {
            $lat_found = false;
            $lng_found = false;
            
            for ($i = 0; $i < min(10, count($row)); $i++) {
                $val = floatval($row[$i]);
                if ($val >= 35 && $val <= 48) {
                    $lat_found = true;
                } elseif ($val >= 6 && $val <= 19) {
                    $lng_found = true;
                }
            }
            
            if ($lat_found && $lng_found) {
                echo "   ✓ Dati numerici rilevati alla riga $row_count (possibile inizio dati)\n";
                $data_start_row = $row_count;
                break;
            }
        }
    }
    
    if (!$header_found && $data_start_row == 0) {
        echo "   ⚠ Header non trovato, assumo formato standard\n";
        $data_start_row = 1;
    }
    
    echo "\n5. Mappatura colonne...\n";
    
    // Mappa delle colonne per i diversi TR
    $tr_columns = [];
    $coordinate_columns = ['lat' => -1, 'lng' => -1];
    
    if ($header_row) {
        for ($i = 0; $i < count($header_row); $i++) {
            $cell = strtolower(trim($header_row[$i]));
            
            // Coordinate
            if ((strpos($cell, 'lat') !== false || strpos($cell, 'latitude') !== false) && $coordinate_columns['lat'] == -1) {
                $coordinate_columns['lat'] = $i;
                echo "   Latitudine: colonna $i\n";
            } elseif ((strpos($cell, 'lon') !== false || strpos($cell, 'longitude') !== false) && $coordinate_columns['lng'] == -1) {
                $coordinate_columns['lng'] = $i;
                echo "   Longitudine: colonna $i\n";
            }
            
            // Parametri sismici per diversi TR
            foreach ([30, 50, 72, 101, 140, 201, 475, 975, 2475] as $tr) {
                if (strpos($cell, "ag_$tr") !== false || strpos($cell, "ag$tr") !== false) {
                    if (!isset($tr_columns[$tr])) $tr_columns[$tr] = [];
                    $tr_columns[$tr]['ag'] = $i;
                    echo "   AG_$tr: colonna $i\n";
                } elseif (strpos($cell, "fo_$tr") !== false || strpos($cell, "fo$tr") !== false || strpos($cell, "f0_$tr") !== false) {
                    if (!isset($tr_columns[$tr])) $tr_columns[$tr] = [];
                    $tr_columns[$tr]['fo'] = $i;
                    echo "   FO_$tr: colonna $i\n";
                } elseif (strpos($cell, "tc_$tr") !== false || strpos($cell, "tc$tr") !== false) {
                    if (!isset($tr_columns[$tr])) $tr_columns[$tr] = [];
                    $tr_columns[$tr]['tc'] = $i;
                    echo "   TC_$tr: colonna $i\n";
                }
            }
        }
    } else {
        echo "   ⚠ Usando mappatura automatica delle colonne\n";
        // Mappatura automatica basata sulla posizione
        $coordinate_columns['lat'] = 0;
        $coordinate_columns['lng'] = 1;
        
        // Assumiamo che le colonne siano organizzate in gruppi di 3 (ag, fo, tc) per ogni TR
        $col_index = 2;
        foreach ([30, 50, 72, 101, 140, 201, 475, 975, 2475] as $tr) {
            $tr_columns[$tr] = [
                'ag' => $col_index,
                'fo' => $col_index + 1,
                'tc' => $col_index + 2
            ];
            $col_index += 3;
        }
    }
    
    echo "\n6. Importazione dati...\n";
    
    if ($coordinate_columns['lat'] == -1 || $coordinate_columns['lng'] == -1) {
        die("ERRORE: Impossibile identificare le colonne delle coordinate\n");
    }
    
    $total_rows = 0;
    $updated_rows = 0;
    $skipped_rows = 0;
    
    // Prepara gli statement di update per ogni TR
    $update_statements = [];
    foreach ($tr_columns as $tr => $columns) {
        if (isset($columns['ag']) && isset($columns['fo']) && isset($columns['tc'])) {
            $update_statements[$tr] = $conn->prepare("
                UPDATE seismic_grid_points 
                SET ag_$tr = ?, fo_$tr = ?, tc_$tr = ?
                WHERE ABS(latitude - ?) < 0.001 AND ABS(longitude - ?) < 0.001
            ");
        }
    }
    
    echo "   Statement preparati per TR: " . implode(', ', array_keys($update_statements)) . "\n";
    
    // Rileggi il file per l'importazione
    $rows = $xlsx->rows(0);
    $current_row = 0;
    
    foreach ($rows as $row) {
        $current_row++;
        
        // Salta le righe prima dei dati
        if ($current_row < $data_start_row) {
            continue;
        }
        
        $total_rows++;
        
        // Verifica che la riga abbia abbastanza colonne
        if (count($row) < max($coordinate_columns['lat'], $coordinate_columns['lng']) + 1) {
            $skipped_rows++;
            continue;
        }
        
        // Estrai coordinate
        $lat = floatval($row[$coordinate_columns['lat']]);
        $lng = floatval($row[$coordinate_columns['lng']]);
        
        // Verifica validità coordinate Italia
        if ($lat < 35 || $lat > 48 || $lng < 6 || $lng > 19) {
            $skipped_rows++;
            continue;
        }
        
        // Salta righe di intestazione ricorsive
        if (abs($lat - 42) < 0.1 && abs($lng - 12) < 0.1 && 
            (strpos(strtolower($row[0]), 'lat') !== false || strpos(strtolower($row[1]), 'lon') !== false)) {
            $skipped_rows++;
            continue;
        }
        
        // Aggiorna i dati per ogni TR
        $row_updated = false;
        foreach ($update_statements as $tr => $stmt) {
            $columns = $tr_columns[$tr];
            
            if (count($row) > max($columns['ag'], $columns['fo'], $columns['tc'])) {
                $ag = floatval($row[$columns['ag']]);
                $fo = floatval($row[$columns['fo']]);
                $tc = floatval($row[$columns['tc']]);
                
                // Verifica validità dati sismici
                if ($ag > 0 && $fo > 0 && $tc > 0) {
                    // Converti ag da g a m/s² se necessario
                    if ($ag < 1.0) {
                        $ag = $ag * 9.81;
                    }
                    
                    $stmt->execute([$ag, $fo, $tc, $lat, $lng]);
                    if ($stmt->rowCount() > 0) {
                        $row_updated = true;
                    }
                }
            }
        }
        
        if ($row_updated) {
            $updated_rows++;
        } else {
            $skipped_rows++;
        }
        
        if ($total_rows % 1000 == 0) {
            echo "   Processate $total_rows righe, aggiornate $updated_rows, saltate $skipped_rows...\n";
        }
    }
    
    echo "\n7. Risultati importazione...\n";
    echo "   Righe totali processate: $total_rows\n";
    echo "   Righe aggiornate: $updated_rows\n";
    echo "   Righe saltate: $skipped_rows\n\n";
    
    // Test finale
    echo "8. Test finale...\n";
    
    require_once 'includes/SeismicCalculator.php';
    $calculator = new SeismicCalculator();
    
    $lat = 42.417406;
    $lng = 14.165970;
    
    $reference_values = [
        'SLV' => ['TR' => 712, 'ag' => 0.180, 'F0' => 2.481, 'TC' => 0.358],
        'SLC' => ['TR' => 1462, 'ag' => 0.232, 'F0' => 2.504, 'TC' => 0.362]
    ];
    
    foreach ($reference_values as $state => $ref) {
        echo "   $state (TR={$ref['TR']}):\n";
        $result = $calculator->interpolateForTR($lat, $lng, $ref['TR']);
        
        foreach (['ag', 'F0', 'TC'] as $param) {
            $param_key = ($param == 'TC') ? 'TC*' : $param;
            $expected = $ref[$param];
            $calculated = $result[$param_key];
            $error = abs($calculated - $expected) / $expected * 100;
            
            $status = $error < 10 ? "✓" : "✗";
            echo "     $param: " . number_format($calculated, 4) . " (atteso: " . number_format($expected, 4) . ", errore: " . number_format($error, 1) . "%) $status\n";
        }
        echo "\n";
    }
    
    echo "=== IMPORTAZIONE CONVERSIONE COMPLETATA ===\n";
    
} catch (Exception $e) {
    echo "ERRORE: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}
?>
