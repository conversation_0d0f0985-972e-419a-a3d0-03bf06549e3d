<?php
/**
 * Test dell'applicazione principale con i parametri di test
 */

echo "<h2>Test Applicazione Principale</h2>\n";

// Simula una richiesta POST con i parametri di test
$_POST = [
    'lat' => '42.417406',
    'lng' => '14.165970',
    'soil_category' => 'B',
    'topographic_category' => 'T1',
    'building_class' => 'II',
    'nominal_life' => '50',
    'damping' => '5'
];

// Include il file di calcolo
ob_start();
include 'api/calculate_seismic_params.php';
$output = ob_get_clean();

echo "<h3>Risultato API</h3>\n";
echo "<pre style='background: #f5f5f5; padding: 10px;'>";
echo htmlspecialchars($output);
echo "</pre>\n";

// Decodifica il JSON per analisi
$result = json_decode($output, true);

if ($result && isset($result['success']) && $result['success']) {
    echo "<h3><PERSON><PERSON><PERSON></h3>\n";
    
    $reference_values = [
        'SLO' => ['TR' => 45, 'ag' => 0.058, 'F0' => 2.469, 'TC' => 0.302],
        'SLD' => ['TR' => 75, 'ag' => 0.072, 'F0' => 2.461, 'TC' => 0.323],
        'SLV' => ['TR' => 712, 'ag' => 0.180, 'F0' => 2.481, 'TC' => 0.358],
        'SLC' => ['TR' => 1462, 'ag' => 0.232, 'F0' => 2.504, 'TC' => 0.362]
    ];
    
    echo "<table border='1' style='border-collapse: collapse;'>\n";
    echo "<tr><th>Stato Limite</th><th>Parametro</th><th>Valore Atteso</th><th>Valore Calcolato</th><th>Errore %</th><th>Status</th></tr>\n";
    
    foreach ($reference_values as $state => $ref) {
        if (isset($result['data'][$state])) {
            $calc = $result['data'][$state];
            
            foreach (['ag', 'F0', 'TC'] as $param) {
                $param_key = $param;
                if ($param == 'TC') $param_key = 'TC';
                
                $expected = $ref[$param];
                $calculated = $calc[$param_key];
                $error = abs($calculated - $expected) / $expected * 100;
                
                $status = $error < 5 ? '✓ Ottimo' : ($error < 10 ? '⚠ Accettabile' : '✗ Errore');
                $color = $error < 5 ? 'green' : ($error < 10 ? 'orange' : 'red');
                
                echo "<tr>";
                echo "<td>$state</td>";
                echo "<td>$param</td>";
                echo "<td>" . number_format($expected, 4) . "</td>";
                echo "<td>" . number_format($calculated, 4) . "</td>";
                echo "<td style='color: $color;'>" . number_format($error, 1) . "%</td>";
                echo "<td style='color: $color;'>$status</td>";
                echo "</tr>\n";
            }
        }
    }
    echo "</table>\n";
    
    // Focus su SLC
    if (isset($result['data']['SLC'])) {
        $slc = $result['data']['SLC'];
        echo "<h3>Risultato SLC Dettagliato</h3>\n";
        echo "<table border='1' style='border-collapse: collapse;'>\n";
        echo "<tr><th>Parametro</th><th>Valore</th></tr>\n";
        foreach ($slc as $key => $value) {
            echo "<tr><td>$key</td><td>" . (is_numeric($value) ? number_format($value, 4) : $value) . "</td></tr>\n";
        }
        echo "</table>\n";
    }
    
} else {
    echo "<p style='color: red;'>Errore nell'API o risultato non valido</p>\n";
    if ($result && isset($result['error'])) {
        echo "<p>Errore: " . htmlspecialchars($result['error']) . "</p>\n";
    }
}
?>
