<?php
/**
 * Script per analizzare la struttura del file conversione.xlsx
 */

require_once 'includes/SimpleXLSX.php';

use <PERSON><PERSON><PERSON>\SimpleXLSX;

echo "=== ANALISI STRUTTURA CONVERSIONE.XLSX ===\n\n";

$excel_file = 'docs/analisi_normative/conversione.xlsx';

try {
    $xlsx = SimpleXLSX::parse($excel_file);
    
    if (!$xlsx) {
        die("ERRORE: " . SimpleXLSX::parseError() . "\n");
    }
    
    echo "1. Informazioni generali:\n";
    $sheet_names = $xlsx->sheetNames();
    echo "   Fogli: " . count($sheet_names) . " (" . implode(', ', $sheet_names) . ")\n\n";
    
    echo "2. Analisi prime 10 righe:\n";
    echo str_repeat("=", 120) . "\n";
    
    $rows = $xlsx->rows(0);
    $row_count = 0;
    
    foreach ($rows as $row) {
        $row_count++;
        if ($row_count > 10) break;
        
        echo "Riga $row_count (" . count($row) . " colonne):\n";
        
        // Mostra le prime 20 colonne
        for ($i = 0; $i < min(20, count($row)); $i++) {
            $cell = trim($row[$i]);
            if (strlen($cell) > 15) {
                $cell = substr($cell, 0, 12) . "...";
            }
            echo sprintf("  [%2d] %-15s", $i, $cell);
            if (($i + 1) % 5 == 0) echo "\n";
        }
        if (count($row) > 20) {
            echo "  ... (+" . (count($row) - 20) . " colonne)\n";
        }
        echo "\n";
    }
    
    echo "\n3. Analisi header (riga 2):\n";
    echo str_repeat("=", 120) . "\n";
    
    $rows = $xlsx->rows(0);
    $row_count = 0;
    $header_row = null;
    
    foreach ($rows as $row) {
        $row_count++;
        if ($row_count == 2) {
            $header_row = $row;
            break;
        }
    }
    
    if ($header_row) {
        echo "Header completo (" . count($header_row) . " colonne):\n";
        for ($i = 0; $i < count($header_row); $i++) {
            $cell = trim($header_row[$i]);
            echo sprintf("[%2d] %-20s", $i, $cell);
            if (($i + 1) % 4 == 0) echo "\n";
        }
        echo "\n\n";
        
        // Identifica pattern nelle colonne
        echo "4. Pattern identificati:\n";
        echo str_repeat("-", 80) . "\n";
        
        $patterns = [];
        for ($i = 0; $i < count($header_row); $i++) {
            $cell = strtolower(trim($header_row[$i]));
            
            if (strpos($cell, 'lat') !== false) {
                $patterns['coordinate'][] = "Latitudine: colonna $i ($cell)";
            } elseif (strpos($cell, 'lon') !== false) {
                $patterns['coordinate'][] = "Longitudine: colonna $i ($cell)";
            } elseif (strpos($cell, 'ag') !== false) {
                $patterns['ag'][] = "AG: colonna $i ($cell)";
            } elseif (strpos($cell, 'fo') !== false || strpos($cell, 'f0') !== false) {
                $patterns['fo'][] = "FO: colonna $i ($cell)";
            } elseif (strpos($cell, 'tc') !== false) {
                $patterns['tc'][] = "TC: colonna $i ($cell)";
            }
        }
        
        foreach ($patterns as $type => $items) {
            echo ucfirst($type) . ":\n";
            foreach ($items as $item) {
                echo "  $item\n";
            }
            echo "\n";
        }
        
        // Prova a identificare la struttura dei TR
        echo "5. Identificazione TR:\n";
        echo str_repeat("-", 80) . "\n";
        
        // Se le colonne sono organizzate in gruppi, prova a identificarli
        $ag_columns = [];
        $fo_columns = [];
        $tc_columns = [];
        
        for ($i = 0; $i < count($header_row); $i++) {
            $cell = strtolower(trim($header_row[$i]));
            if (strpos($cell, 'ag') !== false) {
                $ag_columns[] = $i;
            } elseif (strpos($cell, 'fo') !== false || strpos($cell, 'f0') !== false) {
                $fo_columns[] = $i;
            } elseif (strpos($cell, 'tc') !== false) {
                $tc_columns[] = $i;
            }
        }
        
        echo "Colonne AG: " . implode(', ', $ag_columns) . "\n";
        echo "Colonne FO: " . implode(', ', $fo_columns) . "\n";
        echo "Colonne TC: " . implode(', ', $tc_columns) . "\n\n";
        
        // Se ci sono 9 colonne per tipo, assumiamo che corrispondano ai 9 TR
        $tr_values = [30, 50, 72, 101, 140, 201, 475, 975, 2475];
        
        if (count($ag_columns) == 9 && count($fo_columns) == 9 && count($tc_columns) == 9) {
            echo "✓ Struttura identificata: 9 TR con 3 parametri ciascuno\n";
            echo "Mappatura proposta:\n";
            for ($i = 0; $i < 9; $i++) {
                echo "  TR {$tr_values[$i]}: AG=col{$ag_columns[$i]}, FO=col{$fo_columns[$i]}, TC=col{$tc_columns[$i]}\n";
            }
        } else {
            echo "⚠ Struttura non standard:\n";
            echo "  AG: " . count($ag_columns) . " colonne\n";
            echo "  FO: " . count($fo_columns) . " colonne\n";
            echo "  TC: " . count($tc_columns) . " colonne\n";
        }
    }
    
    echo "\n6. Analisi dati campione (righe 3-7):\n";
    echo str_repeat("=", 120) . "\n";
    
    $rows = $xlsx->rows(0);
    $row_count = 0;
    
    foreach ($rows as $row) {
        $row_count++;
        if ($row_count >= 3 && $row_count <= 7) {
            echo "Riga $row_count:\n";
            
            // Mostra coordinate
            if (count($row) > 2) {
                $lat = floatval($row[2]);
                $lng = floatval($row[1]);
                echo "  Coordinate: Lat=" . number_format($lat, 6) . ", Lng=" . number_format($lng, 6);
                
                if ($lat >= 35 && $lat <= 48 && $lng >= 6 && $lng <= 19) {
                    echo " ✓ (Italia)";
                } else {
                    echo " ✗ (Fuori Italia)";
                }
                echo "\n";
            }
            
            // Mostra primi valori sismici
            if (count($row) > 10) {
                echo "  Primi valori sismici: ";
                for ($i = 3; $i < min(13, count($row)); $i++) {
                    echo number_format(floatval($row[$i]), 3) . " ";
                }
                echo "\n";
            }
            echo "\n";
        }
        if ($row_count > 7) break;
    }
    
} catch (Exception $e) {
    echo "ERRORE: " . $e->getMessage() . "\n";
}
?>
