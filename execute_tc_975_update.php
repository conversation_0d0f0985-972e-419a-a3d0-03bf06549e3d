<?php
/**
 * Script per eseguire l'aggiornamento dei valori tc_975
 */

require_once 'includes/db_config.php';

echo "=== AGGIORNAMENTO VALORI TC_975 ===\n\n";

try {
    $conn = new PDO("mysql:host=localhost;dbname=asdp_db;charset=utf8", "root", "");
    $conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // 1. Backup dei dati originali
    echo "1. Creazione backup...\n";
    $backup_table = "seismic_grid_points_backup_" . date('Y_m_d_H_i_s');
    $conn->exec("CREATE TABLE $backup_table AS SELECT * FROM seismic_grid_points");
    echo "   ✓ Backup creato: $backup_table\n\n";
    
    // 2. Verifica stato attuale
    echo "2. Verifica stato attuale...\n";
    $stmt = $conn->query("SELECT COUNT(*) as zero_count FROM seismic_grid_points WHERE tc_975 = 0");
    $zero_count = $stmt->fetch(PDO::FETCH_ASSOC)['zero_count'];
    echo "   Punti con tc_975 = 0: $zero_count\n\n";
    
    // 3. Aggiornamento valori
    echo "3. Aggiornamento valori tc_975...\n";
    
    $update_sql = "
        UPDATE seismic_grid_points 
        SET tc_975 = CASE 
            WHEN tc_2475 > 1.0 THEN tc_475 + 0.01
            ELSE tc_475 + (tc_2475 - tc_475) * 0.3
        END
        WHERE tc_975 = 0
    ";
    
    $affected_rows = $conn->exec($update_sql);
    echo "   ✓ Aggiornati $affected_rows punti\n\n";
    
    // 4. Verifica risultati
    echo "4. Verifica risultati...\n";
    $stmt = $conn->query("
        SELECT 
            COUNT(*) as total_points,
            COUNT(CASE WHEN tc_975 = 0 THEN 1 END) as zero_values,
            MIN(tc_975) as min_value,
            MAX(tc_975) as max_value,
            AVG(tc_975) as avg_value
        FROM seismic_grid_points
    ");
    
    $stats = $stmt->fetch(PDO::FETCH_ASSOC);
    
    echo "   Punti totali: {$stats['total_points']}\n";
    echo "   Valori zero: {$stats['zero_values']}\n";
    echo "   Valore minimo: " . number_format($stats['min_value'], 3) . "\n";
    echo "   Valore massimo: " . number_format($stats['max_value'], 3) . "\n";
    echo "   Valore medio: " . number_format($stats['avg_value'], 3) . "\n\n";
    
    // 5. Test con coordinate di riferimento
    echo "5. Test con coordinate di riferimento...\n";
    
    require_once 'includes/SeismicCalculator.php';
    $calculator = new SeismicCalculator();
    
    $lat = 42.417406;
    $lng = 14.165970;
    
    $reference_values = [
        'SLV' => ['TR' => 712, 'ag' => 0.180, 'F0' => 2.481, 'TC' => 0.358],
        'SLC' => ['TR' => 1462, 'ag' => 0.232, 'F0' => 2.504, 'TC' => 0.362]
    ];
    
    foreach ($reference_values as $state => $ref) {
        echo "   $state (TR={$ref['TR']}):\n";
        $result = $calculator->interpolateForTR($lat, $lng, $ref['TR']);
        
        foreach (['ag', 'F0', 'TC'] as $param) {
            $param_key = ($param == 'TC') ? 'TC*' : $param;
            $expected = $ref[$param];
            $calculated = $result[$param_key];
            $error = abs($calculated - $expected) / $expected * 100;
            
            $status = $error < 10 ? "✓" : "✗";
            echo "     $param: " . number_format($calculated, 4) . " (atteso: " . number_format($expected, 4) . ", errore: " . number_format($error, 1) . "%) $status\n";
        }
        echo "\n";
    }
    
    echo "=== AGGIORNAMENTO COMPLETATO CON SUCCESSO ===\n";
    
} catch (Exception $e) {
    echo "ERRORE: " . $e->getMessage() . "\n";
}
?>
