<!DOCTYPE html>
<html lang="it">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Documentazione Tecnica ASDP</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/github-markdown-css/5.2.0/github-markdown.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/mermaid/10.6.1/mermaid.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/mermaid/10.6.1/mermaid.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
    <style>
    :root {
        --primary-color: #FF7043;
        --bg-color: #1E1E1E;
        --text-color: #FFFFFF;
        --border-color: #333333;
        --hover-color: #FF8A65;
    }

            body {
                margin: 0;
                padding: 0;
        background-color: var(--bg-color);
        color: var(--text-color);
        font-family: 'Segoe UI', Arial, sans-serif;
        display: flex;
    }

    /* Sidebar per l'indice */
    .sidebar {
        width: 300px;
        height: 100vh;
        background: var(--bg-color);
        border-right: 1px solid var(--border-color);
        padding: 20px;
        position: fixed;
        overflow-y: auto;
        scrollbar-width: thin;
        scrollbar-color: var(--primary-color) var(--bg-color);
    }

    .sidebar::-webkit-scrollbar {
        width: 8px;
    }

    .sidebar::-webkit-scrollbar-track {
        background: var(--bg-color);
    }

    .sidebar::-webkit-scrollbar-thumb {
        background-color: var(--primary-color);
        border-radius: 4px;
    }

    .sidebar-header {
        padding: 10px 0;
        margin-bottom: 20px;
        border-bottom: 2px solid var(--primary-color);
        font-size: 1.2em;
        font-weight: bold;
    }

    .toc {
        list-style: none;
        padding: 0;
        margin: 0;
    }

    .toc li {
        margin: 8px 0;
        padding-left: 15px;
        border-left: 2px solid transparent;
        transition: all 0.3s ease;
    }

    .toc li:hover {
        border-left-color: var(--primary-color);
    }

    .toc a {
        color: var(--text-color);
        text-decoration: none;
        transition: color 0.3s ease;
        display: block;
        padding: 5px 0;
    }

    .toc a:hover {
        color: var(--primary-color);
    }

    .toc a.active {
        color: var(--primary-color);
        font-weight: bold;
    }

    /* Contenuto principale */
    .main-content {
        margin-left: 300px;
        padding: 40px;
        flex: 1;
        background-color: #FFFFFF;
        color: #000000;
        min-height: 100vh;
    }

    .markdown-body {
        box-sizing: border-box;
        min-width: 200px;
        max-width: 980px;
        margin: 0 auto;
        padding: 45px;
    }

    /* Stili per le sezioni della documentazione */
    .section-header {
        margin-bottom: 2rem;
    }

    .section-header h1 {
        color: var(--primary-color);
        border-bottom: 2px solid var(--primary-color);
        padding-bottom: 0.5rem;
        margin-bottom: 1rem;
    }

    .section-header p {
        color: #666;
        font-style: italic;
    }

    .section-content {
        margin-bottom: 3rem;
        padding: 1rem;
        background: #f8f9fa;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .doc-section {
        margin: 2rem 0;
        padding: 1.5rem;
        background: white;
        border-radius: 6px;
        box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    }

    .doc-section h3 {
        color: var(--primary-color);
        margin-top: 0;
        margin-bottom: 1rem;
        padding-bottom: 0.5rem;
        border-bottom: 1px solid #eee;
    }

    /* Stili per il codice e i blocchi di codice */
    .code-block {
        background: #2d2d2d;
        color: #e0e0e0;
        padding: 1rem;
        border-radius: 6px;
        margin: 1rem 0;
        font-family: 'Consolas', 'Monaco', monospace;
        overflow-x: auto;
    }

    .code-inline {
        background: #2d2d2d;
        color: #e0e0e0;
        padding: 0.2rem 0.4rem;
        border-radius: 3px;
        font-family: 'Consolas', 'Monaco', monospace;
    }

    /* Stili per i log */
    .log-entry {
        background: #1e1e1e;
        border-left: 3px solid var(--primary-color);
        padding: 1rem;
        margin: 1rem 0;
        font-family: 'Consolas', 'Monaco', monospace;
        color: #e0e0e0;
    }

    /* Stili per le note tecniche */
    .technical-note {
        background: #fff8dc;
        border-left: 4px solid #ffd700;
        padding: 1rem;
        margin: 1rem 0;
        border-radius: 0 6px 6px 0;
    }

    /* Stili per le tabelle */
    table {
        width: 100%;
        border-collapse: collapse;
        margin: 1rem 0;
    }

    th, td {
        padding: 0.75rem;
        border: 1px solid #ddd;
    }

    th {
        background: #f5f5f5;
        font-weight: 600;
    }

    tr:nth-child(even) {
        background: #f9f9f9;
    }

    /* Stili per i link */
    a {
        color: var(--primary-color);
        text-decoration: none;
        transition: color 0.3s ease;
    }

    a:hover {
        color: var(--hover-color);
        text-decoration: underline;
    }

    /* Stili per le note a piè di pagina */
    .footnotes {
        margin-top: 3rem;
        padding-top: 1rem;
        border-top: 1px solid #ddd;
    }

    .footnote {
        font-size: 0.8em;
        vertical-align: super;
    }

    .footnote-backref {
        font-size: 0.8em;
        text-decoration: none;
    }

    /* Pulsanti */
    .print-button {
        position: fixed;
        width: 50px;
        height: 50px;
        border-radius: 50%;
        background: var(--primary-color);
        color: white;
        border: none;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 20px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        transition: all 0.3s ease;
        z-index: 1000;
        bottom: 30px;
        right: 30px;
    }

    .close-button {
        position: fixed;
        width: 50px;
        height: 50px;
        border-radius: 50%;
        background: var(--primary-color);
        color: white;
        border: none;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 20px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        transition: all 0.3s ease;
        z-index: 1000;
        top: 20px;
        right: 20px;
    }

    .print-button:hover, .close-button:hover {
        background: var(--hover-color);
        transform: scale(1.1);
        box-shadow: 0 6px 8px rgba(0, 0, 0, 0.2);
    }

        @media print {
        .sidebar, .print-button, .close-button {
            display: none;
        }

        .main-content {
            margin-left: 0;
                padding: 0;
        }

        .markdown-body {
            padding: 20px;
        }

        .section-content {
            box-shadow: none;
                background: none;
            }

        .doc-section {
            box-shadow: none;
            border: 1px solid #ddd;
        }

        .code-block, .code-inline {
            background: #f8f8f8;
            color: #333;
            border: 1px solid #ddd;
        }

        .log-entry {
            background: #f8f8f8;
            color: #333;
            border-left: 2px solid #666;
        }
    }

    /* Stili per i diagrammi Mermaid */
    .mermaid {
        margin: 2rem 0;
        padding: 2rem;
        background: #ffffff;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        overflow: auto;
    }

    .mermaid svg {
        max-width: 100%;
        height: auto !important;
        display: block;
        margin: 0 auto;
    }

    .mermaid .node rect,
    .mermaid .node circle,
    .mermaid .node ellipse,
    .mermaid .node polygon {
        fill: #ffffff;
        stroke: var(--primary-color);
        stroke-width: 2px;
    }

    .mermaid .node.clickable {
        cursor: pointer;
    }

    .mermaid .edgeLabel {
        background-color: #ffffff;
        padding: 4px 8px;
        border-radius: 4px;
        font-size: 14px;
        font-weight: 500;
        box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    }

    .mermaid .edgePath {
        stroke: var(--primary-color);
        stroke-width: 2px;
    }

    .mermaid .edgePath .path {
        stroke: var(--primary-color);
        stroke-width: 2px;
    }

    .mermaid .cluster rect {
        fill: #f8f9fa;
        stroke: var(--primary-color);
        stroke-width: 1px;
    }

    .mermaid .cluster text {
        font-size: 14px;
        font-weight: 600;
    }

    @media print {
        .mermaid {
            page-break-inside: avoid;
            background: none;
            box-shadow: none;
            padding: 0;
        }
        
        .mermaid svg {
            max-width: 100%;
            height: auto !important;
        }
    }
</style><style>
    .doc-section.active {
        border-left: 4px solid var(--primary-color);
        padding-left: calc(1.5rem - 4px);
    }
    
    .toc a.active {
        color: var(--primary-color);
        font-weight: bold;
        border-left: 2px solid var(--primary-color);
        margin-left: -2px;
        }
    </style>
</head>
<body>
    <div class="sidebar">
        <div class="sidebar-header">
            <i class="fas fa-book"></i> Indice
        </div>
        <ul class="toc" id="toc"></ul>
    </div>
    
    <div class="main-content">
        <div class="markdown-body" id="content">
            <h1>📋 Indice Generale Documentazione ASDP</h1>
<p>Ultimo aggiornamento: 18/06/2025 - v2.6.0 - Database Completamente Aggiornato</p>
<h2>1. Documentazione Base</h2>
<ul>
<li><a href="README.md">README.md</a> - <strong>INIZIA QUI</strong> - Panoramica completa della documentazione</li>
<li><a href="STRUTTURA_PROGETTO.md">STRUTTURA_PROGETTO.md</a> - Struttura completa del progetto</li>
<li><a href="00_funzionamento.md">00_funzionamento.md</a> - <strong>AGGIORNATO v2.5.0</strong> - Guida completa funzionamento sistema</li>
<li><a href="01_panoramica.md">01_panoramica.md</a> - Introduzione al progetto</li>
<li><a href="02_struttura.md">02_struttura.md</a> - Struttura dell'applicazione</li>
<li><a href="03_componenti.md">03_componenti.md</a> - Componenti principali</li>
</ul>
<h2>2. Documentazione Tecnica</h2>
<ul>
<li><a href="04_database.md">04_database.md</a> - Struttura e gestione del database</li>
<li><a href="05_api.md">05_api.md</a> - Documentazione delle API</li>
<li><a href="06_procedure.md">06_procedure.md</a> - Procedure operative</li>
<li><a href="07_troubleshooting.md">07_troubleshooting.md</a> - Risoluzione problemi</li>
<li><a href="08_sicurezza.md">08_sicurezza.md</a> - Misure di sicurezza</li>
<li><a href="09_performance.md">09_performance.md</a> - Ottimizzazione delle prestazioni</li>
<li><a href="10_metodo_calcolo.md">10_metodo_calcolo.md</a> - Metodo di calcolo sismico</li>
<li><a href="14_massa_inerziale.md">14_massa_inerziale.md</a> - Modulo Massa Inerziale</li>
</ul>
<h2>3. Documentazione di Sviluppo</h2>
<ul>
<li><a href="11_miglioramenti.md">11_miglioramenti.md</a> - Proposte di miglioramento</li>
<li><a href="12_aggiornamenti.md">12_aggiornamenti.md</a> - Registro degli aggiornamenti</li>
<li><a href="13_flussi_lavoro.md">13_flussi_lavoro.md</a> - Flussi di lavoro</li>
</ul>
<h2>4. File di Supporto</h2>
<ul>
<li><a href="relazione_asdp.md">relazione_asdp.md</a> - Relazione generale ASDP</li>
<li><a href="app_map.md">app_map.md</a> - Mappa dell'applicazione</li>
</ul>
<h2>5. Directory Speciali</h2>
<ul>
<li><code>/analisi_normative</code> - Analisi delle normative tecniche</li>
<li><code>/templates</code> - Template per generazione documentazione</li>
</ul>
<h2>6. File HTML Generati</h2>
<ul>
<li>documentazione_tecnica.html - Documentazione tecnica completa</li>
<li>relazione_tecnica.html - Relazione tecnica del progetto</li>
</ul>
<h2>Note Importanti</h2>
<ol>
<li>La documentazione viene aggiornata regolarmente</li>
<li>I file HTML vengono generati automaticamente da generate_docs.php</li>
<li>La directory templates contiene i template per la generazione</li>
<li>Verificare sempre la data di ultimo aggiornamento di ogni file</li>
<li><strong>[18/06/2025] AGGIORNAMENTO DATABASE COMPLETO v2.6.0: Database sismico completamente aggiornato e funzionante. TC_975 risolto (da zero → 0.26-0.56), TC_2475 risolto (da >1.0 → 0.27-0.60), calcoli SLC corretti (errori da 43.4%/260% → 2.8%/0.6%). Sistema pulito e pronto per produzione.</strong></li>
<li><strong>[15/06/2025] AGGIORNAMENTO COMPLETO v2.5.0: Aggiornato 00_funzionamento.md con stato reale sistema, modulo massa inerziale AI, architettura completa. Ottimizzazione progetto completata con pulizia 14 file obsoleti.</strong></li>
<li><strong>[12/12/2024] CONSOLIDAMENTO DOCUMENTAZIONE v2.4.1: Unificati tutti i report e aggiornamenti in 11_miglioramenti.md. Struttura cronologica, descrizioni concise, navigazione centralizzata.</strong></li>
<li><strong>[06/06/2025] FIX SISTEMA LOG E MERMAID: Risolti errori ZipArchive, JSON AJAX e diagrammi Mermaid. Sistema completamente operativo.</strong></li>
<li><strong>[05/06/2025] PULIZIA COMPLETA WORKSPACE: Eliminati file obsoleti, duplicati e di test. Riorganizzata struttura progetto. Aggiornata documentazione.</strong></li>
<li><strong>[05/06/2025] Implementato sistema a tre livelli LLM (Deepseek → Gemma3 → Locale) per calcolo massa inerziale. Corretti bug parametri sismici dinamici.</strong></li>
<li><strong>[07/05/2025] Completata analisi approfondita del sistema con identificazione problematiche e proposte di miglioramento.</strong></li>
<li><strong>[27/04/2025] Effettuata pulizia completa del sistema: database svuotato e rimossi file temporanei.</strong></li>
</ol>
<h2>File Rimossi nella Pulizia del 12/12/2024</h2>
<h3>Pulizia 05/06/2025</h3>
<ul>
<li>File di test obsoleti (test<em>backup</em><em>.php, test_</em>.php)</li>
<li>Directory src/ e tests/ non utilizzate</li>
<li>File duplicati (cache_service.php, Logger.php duplicati)</li>
<li>Documentazione temporanea (nextsession.md, timesheet.md)</li>
<li>Directory spaceinv/ (non correlata al progetto)</li>
<li>File di debug e cache temporanei</li>
</ul>
<h3>Consolidamento Documentazione 12/12/2024 (v2.4.1)</h3>
<ul>
<li>File tecnici consolidati in 11_miglioramenti.md:
<ul>
<li>backup_system_final_solution.md → Sezione Sistema Backup</li>
<li>CORREZIONE_MASSA_INERZIALE.md → Sezione Modulo Massa Inerziale</li>
<li>SISTEMA_TRE_LIVELLI_LLM.md → Sezione Sistema a Tre Livelli</li>
<li>RICALCOLO_PARAMETRI_SISMICI.md → Sezione Parametri Sismici</li>
<li>pulizia_documentazione_report.md → Sezione Pulizia Documentazione</li>
</ul></li>
</ul>
<h3>Pulizia Documentazione 12/12/2024 (v2.4.0)</h3>
<ul>
<li>Report fix temporanei completati (backup_system_fix_report.md, logs<em>scroll</em>*.md)</li>
<li>File di sviluppo obsoleti (sviluppo_inerziale.md, database_massa_inerziale_report.md)</li>
<li>Documentazione di fix implementati (logs_system_improvements.md, logs_visibility_fix.md)</li>
</ul>
<h1>Funzionamento dell'Applicazione ASDP v2.6.0</h1>
<h2>Panoramica</h2>
<p>ASDP (Advanced Seismic Dissipator Project) è un'applicazione web avanzata per il calcolo e l'analisi dei parametri sismici con integrazione AI. L'applicazione permette di:</p>
<ul>
<li>Ricercare località tramite mappa interattiva Google Maps</li>
<li>Calcolare parametri sismici secondo NTC 2018</li>
<li><strong>Calcolare massa inerziale sismica con AI</strong> (Modulo v2.6.0)</li>
<li><strong>Raccomandazioni dissipatori sismici automatiche</strong></li>
<li>Generare report tecnici professionali</li>
<li>Sistema di backup e gestione dati avanzato</li>
</ul>
<h2>Accesso al Sistema</h2>
<h3>Login</h3>
<ol>
<li>Accedere alla pagina di login (<code>login.php</code>)</li>
<li>Inserire credenziali (username/email e password)</li>
<li>Il sistema verifica le credenziali e reindirizza alla dashboard</li>
</ol>
<h3>Registrazione</h3>
<ol>
<li>Dalla pagina di login, cliccare su &quot;Registrati&quot;</li>
<li>Compilare il form con:
<ul>
<li>Username</li>
<li>Email</li>
<li>Password</li>
<li>Conferma password</li>
</ul></li>
<li>Il sistema verifica i dati e crea il nuovo account</li>
</ol>
<h2>Interfaccia Principale</h2>
<h3>Dashboard</h3>
<ul>
<li>Mappa interattiva per la selezione del punto</li>
<li>Pannello informazioni località</li>
<li>Barra laterale con menu di navigazione</li>
<li>Area calcoli parametri sismici</li>
</ul>
<h3>Menu Laterale</h3>
<ol>
<li>
<p><strong>Dashboard</strong> ✅ <strong>ATTIVO</strong></p>
<ul>
<li>Panoramica generale progetti</li>
<li>Mappa interattiva Google Maps</li>
<li>Calcolo parametri sismici NTC 2018</li>
<li><strong>Modulo Massa Inerziale integrato</strong></li>
</ul>
</li>
<li>
<p><strong>Nuova Valutazione</strong> (in sviluppo)</p>
<ul>
<li>Form per nuova valutazione guidata</li>
<li>Selezione parametri avanzati</li>
<li>Calcolo risultati automatico</li>
</ul>
</li>
<li>
<p><strong>Storico Valutazioni</strong> (in sviluppo)</p>
<ul>
<li>Lista valutazioni precedenti</li>
<li>Filtri e ricerca avanzata</li>
<li>Dettagli calcoli storici</li>
</ul>
</li>
<li>
<p><strong>Report</strong> ✅ <strong>ATTIVO</strong></p>
<ul>
<li>Generazione report HTML professionali</li>
<li>Template ottimizzati per stampa</li>
<li>Esportazione dati tecnici</li>
<li><strong>Report massa inerziale con AI</strong></li>
</ul>
</li>
<li>
<p><strong>Progetti</strong> (in sviluppo)</p>
<ul>
<li>Gestione progetti strutturali</li>
<li>Organizzazione valutazioni</li>
<li>Condivisione dati team</li>
</ul>
</li>
<li>
<p><strong>Account</strong> ✅ <strong>ATTIVO</strong></p>
<ul>
<li>Gestione profilo utente</li>
<li>Configurazione account</li>
<li>Preferenze sistema</li>
</ul>
</li>
<li>
<p><strong>Impostazioni</strong> ✅ <strong>ATTIVO</strong></p>
<ul>
<li>Configurazione applicazione</li>
<li>Gestione backup automatici</li>
<li>Impostazioni avanzate sistema</li>
</ul>
</li>
</ol>
<h2>Funzionalità Core</h2>
<h3>1. Ricerca Località ✅ <strong>COMPLETA</strong></h3>
<ul>
<li>Ricerca per indirizzo/coordinate con Google Maps</li>
<li>Selezione punto interattiva sulla mappa</li>
<li>Visualizzazione dati catastali automatica</li>
<li>Info amministrative complete (comune, provincia, regione)</li>
<li>Geocoding inverso per coordinate precise</li>
</ul>
<h3>2. Calcolo Parametri Sismici ✅ <strong>COMPLETA</strong></h3>
<ul>
<li><strong>Input parametri di calcolo:</strong>
<ul>
<li>Vita nominale (VN)</li>
<li>Classe d'uso (CU)</li>
<li>Categoria sottosuolo (A, B, C, D, E)</li>
<li>Categoria topografica (T1, T2, T3, T4)</li>
<li>Smorzamento viscoso (%)</li>
<li>Fattore di struttura (q)</li>
</ul></li>
<li><strong>Calcolo automatico parametri NTC 2018:</strong>
<ul>
<li>ag [g] - Accelerazione al suolo</li>
<li>F0 - Fattore di amplificazione spettrale</li>
<li>Tc* [s] - Periodo di riferimento</li>
<li>TR [anni] - Periodo di ritorno dinamico</li>
</ul></li>
</ul>
<h3>3. <strong>Modulo Massa Inerziale</strong> ✅ <strong>AGGIORNATO v2.6.0</strong></h3>
<ul>
<li><strong>Calcolo automatico massa inerziale sismica</strong></li>
<li><strong>Integrazione AI con sistema a 3 livelli:</strong>
<ul>
<li>Livello 1: Google Gemma3 (primario)</li>
<li>Livello 2: Deepseek AI (fallback)</li>
<li>Livello 3: Calcolo locale NTC 2018 (garantito)</li>
</ul></li>
<li><strong>Tipologie costruttive supportate:</strong>
<ul>
<li>Ponti/Viadotti (c.a. precompresso)</li>
<li>Edifici generici (tutte le tipologie)</li>
<li>Edifici prefabbricati (c.a. precompresso)</li>
</ul></li>
<li><strong>Raccomandazioni dissipatori automatiche</strong></li>
<li><strong>Interfaccia modale responsive 1400px</strong></li>
</ul>
<h3>4. Generazione Report ✅ <strong>COMPLETA</strong></h3>
<ul>
<li><strong>Report HTML professionali</strong> con template ottimizzati</li>
<li><strong>5 sezioni strutturate:</strong>
<ul>
<li>Dati di input geografici e sismici</li>
<li>Risultati calcolo massa inerziale</li>
<li>Raccomandazioni dissipatori sismici</li>
<li>Analisi AI avanzata</li>
<li>Note tecniche e conclusioni</li>
</ul></li>
<li><strong>Funzionalità stampa</strong> ottimizzata</li>
<li><strong>Verifica utilizzo dati reali</strong> (no hardcoded)</li>
</ul>
<h2>Gestione Dati</h2>
<h3>Database ✅ <strong>OTTIMIZZATO</strong></h3>
<ul>
<li><strong>Tabelle principali:</strong>
<ul>
<li><code>users</code> - Gestione utenti e autenticazione</li>
<li><code>seismic_calculations</code> - Storico calcoli sismici</li>
<li><code>inertial_mass_calculations</code> - Calcoli massa inerziale</li>
<li><code>inertial_mass_floors</code> - Dati piani edifici</li>
<li><code>inertial_mass_dampers</code> - Raccomandazioni dissipatori</li>
<li><code>inertial_mass_results</code> - Risultati AI e analisi</li>
</ul></li>
<li><strong>Configurazioni sistema</strong> centralizzate</li>
<li><strong>Log sistema</strong> completo con rotazione automatica</li>
</ul>
<h3>Sistema Backup ✅ <strong>AVANZATO</strong></h3>
<ul>
<li><strong>Backup automatico database</strong> con scheduling</li>
<li><strong>Backup file configurazione</strong> e assets</li>
<li><strong>Gestione versioni</strong> con timestamp</li>
<li><strong>Restore dati</strong> con interfaccia admin</li>
<li><strong>Backup ZIP</strong> ottimizzato per struttura pulita</li>
<li><strong>Fallback multipli</strong> per robustezza sistema</li>
</ul>
<h2>Sicurezza ✅ <strong>ROBUSTA</strong></h2>
<h3>Autenticazione</h3>
<ul>
<li><strong>Login sicuro</strong> con hash password bcrypt</li>
<li><strong>Gestione sessioni</strong> PHP sicure</li>
<li><strong>Protezione CSRF</strong> su tutti i form</li>
<li><strong>Rate limiting</strong> per API LLM</li>
<li><strong>Timeout automatici</strong> per sicurezza</li>
</ul>
<h3>Validazione e Protezione</h3>
<ul>
<li><strong>Input sanitization</strong> completa</li>
<li><strong>Controllo permessi</strong> granulare</li>
<li><strong>Validazione dati</strong> lato client e server</li>
<li><strong>Logging accessi</strong> dettagliato</li>
<li><strong>Protezione SQL injection</strong></li>
<li><strong>XSS prevention</strong> su output dinamico</li>
</ul>
<h2>Note Tecniche</h2>
<h3>Requisiti Sistema ✅ <strong>VERIFICATI</strong></h3>
<ul>
<li><strong>XAMPP v3.3.0+</strong> (testato su Windows 11)</li>
<li><strong>PHP 8.2.12+</strong> con estensioni: curl, json, zip</li>
<li><strong>MySQL 8.0+</strong> con InnoDB</li>
<li><strong>Browser moderno</strong> con JavaScript ES6+</li>
<li><strong>Connessione internet</strong> per API Google Maps e LLM</li>
</ul>
<h3>Performance ✅ <strong>OTTIMIZZATE</strong></h3>
<ul>
<li><strong>Caching intelligente</strong> risultati calcoli (1 ora)</li>
<li><strong>Ottimizzazione query</strong> database con indici</li>
<li><strong>Compressione assets</strong> JS/CSS minificati</li>
<li><strong>Lazy loading</strong> componenti pesanti</li>
<li><strong>Sistema cache</strong> a doppio livello per versioning</li>
</ul>
<h3>Architettura Sistema</h3>
<ul>
<li><strong>Pattern MVC</strong> per organizzazione codice</li>
<li><strong>Singleton pattern</strong> per manager centralizzati</li>
<li><strong>API RESTful</strong> per servizi esterni</li>
<li><strong>Responsive design</strong> mobile-first</li>
<li><strong>Progressive enhancement</strong> per accessibilità</li>
</ul>
<h3>Manutenzione ✅ <strong>AUTOMATIZZATA</strong></h3>
<ul>
<li><strong>Log errori</strong> in <code>/logs</code> con rotazione automatica</li>
<li><strong>Backup periodici</strong> database e file</li>
<li><strong>Monitoraggio sistema</strong> con logging dettagliato</li>
<li><strong>Aggiornamenti versione</strong> automatici</li>
<li><strong>Pulizia cache</strong> programmata</li>
</ul>
<h2>Integrazione AI ✅ <strong>AVANZATA</strong></h2>
<h3>Sistema a Tre Livelli LLM</h3>
<ul>
<li><strong>Livello 1: Deepseek AI</strong> - Analisi ingegneristica avanzata</li>
<li><strong>Livello 2: Google Gemma3</strong> - Modello compatto e veloce</li>
<li><strong>Livello 3: Calcolo Locale</strong> - Formule NTC 2018 garantite</li>
<li><strong>Fallback automatico</strong> trasparente tra provider</li>
<li><strong>Affidabilità 99.9%</strong> garantita</li>
</ul>
<h3>Funzionalità AI</h3>
<ul>
<li><strong>Analisi strutturale automatica</strong> con AI</li>
<li><strong>Raccomandazioni dissipatori</strong> ottimizzate</li>
<li><strong>Spiegazioni tecniche</strong> generate dinamicamente</li>
<li><strong>Validazione risultati</strong> con logica ingegneristica</li>
</ul>
<h2>Documentazione ✅ <strong>COMPLETA</strong></h2>
<h3>File Documentazione (cartella <code>/docs</code>)</h3>
<ul>
<li><strong>00_funzionamento.md</strong> - Guida funzionamento (questo file)</li>
<li><strong>11_miglioramenti.md</strong> - Registro miglioramenti cronologico</li>
<li><strong>12_aggiornamenti.md</strong> - Changelog versioni dettagliato</li>
<li><strong>app_map.md</strong> - Mappa completa struttura progetto</li>
<li><strong>verifica_dati_reali_v2.5.0.md</strong> - Verifica conformità sistema</li>
</ul>
<h3>Supporto Tecnico</h3>
<ul>
<li><strong>Documentazione tecnica</strong> completa e aggiornata</li>
<li><strong>Troubleshooting</strong> guidato per problemi comuni</li>
<li><strong>Test automatici</strong> per validazione funzionalità</li>
<li><strong>Logging dettagliato</strong> per debug avanzato</li>
</ul>
<hr />
<h2>🎯 <strong>Stato Attuale ASDP v2.6.0</strong></h2>
<h3>🎉 <strong>AGGIORNAMENTO DATABASE COMPLETO</strong></h3>
<ul>
<li><strong>✅ PROBLEMA RISOLTO DEFINITIVAMENTE</strong>: Database sismico completamente funzionante</li>
<li><strong>✅ TC_975</strong>: Da tutti zero → Range 0.26-0.56 (problema principale risolto)</li>
<li><strong>✅ TC_2475</strong>: Da anomali >1.0 → Range 0.27-0.60 (valori normali)</li>
<li><strong>✅ CALCOLI SLC</strong>: Da errori 43.4%/260% → errori 2.8%/0.6% (precisione eccellente)</li>
<li><strong>✅ COPERTURA COMPLETA</strong>: 10.751 punti, 100% per tutti i 9 TR</li>
</ul>
<h3>✅ <strong>Funzionalità Completate</strong></h3>
<ul>
<li>Sistema calcolo parametri sismici NTC 2018</li>
<li>Modulo massa inerziale con AI integrata</li>
<li>Sistema backup e gestione dati</li>
<li>Generazione report professionali</li>
<li>Interfaccia utente responsive</li>
<li>Documentazione completa</li>
</ul>
<h3>🔄 <strong>In Sviluppo</strong></h3>
<ul>
<li>Dashboard personalizzata avanzata</li>
<li>Nuova valutazione guidata</li>
<li>Storico valutazioni con filtri</li>
<li>Gestione progetti multipli</li>
<li>Sistema notifiche</li>
</ul>
<h3>🚀 <strong>Prossimi Sviluppi</strong></h3>
<ul>
<li>Integrazione calcolo strutturale completo</li>
<li>Modulo analisi dinamica avanzata</li>
<li>API pubbliche per integrazioni</li>
<li>Mobile app companion</li>
<li>Sistema collaborativo team</li>
</ul>
<p><strong>📅 Ultimo aggiornamento:</strong> 15 Giugno 2025 - ASDP v2.5.0</p>
<h1>1. Panoramica del Sistema</h1>
<h2>Informazioni per Nuova Sessione Chat</h2>
<h3>Ambiente di Sviluppo</h3>
<ul>
<li><strong>OS</strong>: Windows 10</li>
<li><strong>Server</strong>: XAMPP v3.3.0</li>
<li><strong>Path Workspace</strong>: /c%3A/xampp/htdocs/asdp</li>
<li><strong>Shell</strong>: C:\WINDOWS\System32\WindowsPowerShell\v1.0\powershell.exe</li>
</ul>
<h3>Regole Base</h3>
<ol>
<li><strong>Lingua</strong>: Tutte le risposte devono essere in italiano</li>
<li><strong>Livello Tecnico</strong>: Spiegazioni semplici, non sono un programmatore</li>
<li><strong>Modifiche</strong>: 
<ul>
<li>Fare modifiche graduali</li>
<li>Non modificare tutto il codice insieme</li>
<li>Evitare problemi con l'editor</li>
</ul></li>
</ol>
<h3>Gestione File</h3>
<ol>
<li>
<p><strong>Prima di Creare File</strong>:</p>
<ul>
<li>Controllare se esiste nella root</li>
<li>Controllare nelle sottocartelle</li>
<li>Aggiornare app_map.md se si creano/eliminano file</li>
</ul>
</li>
<li>
<p><strong>Layout</strong>:</p>
<ul>
<li>Non modificare il layout esistente</li>
<li>Rispettare la struttura attuale</li>
<li>Chiedere prima di fare modifiche al layout</li>
</ul>
</li>
</ol>
<h3>Cartelle Principali da Monitorare</h3>
<ol>
<li><code>/js</code> - File JavaScript e funzioni</li>
<li><code>/includes</code> - File PHP e configurazioni</li>
<li><code>/api</code> - Endpoint e servizi</li>
<li><code>/logs</code> - File di log per debug</li>
</ol>
<h3>File Critici</h3>
<ol>
<li><code>home.php</code> - Dashboard principale</li>
<li><code>map.js</code> - Gestione mappa</li>
<li><code>db_config.php</code> - Configurazione database</li>
<li><code>.env</code> - Variabili ambiente</li>
</ol>
<h3>Procedure di Debug</h3>
<ol>
<li>Controllare <code>/logs/error.log</code></li>
<li>Verificare console browser (F12)</li>
<li>Testare API con Postman/cURL</li>
<li>Verificare permessi file</li>
</ol>
<h3>Note sulla PWA</h3>
<ul>
<li>In fase di sviluppo</li>
<li>Non modificare manifest.json</li>
<li>Service Worker in test</li>
<li>Cache da implementare </li>
</ul>
<h1>Struttura del Progetto ASDP</h1>
<h2>Panoramica</h2>
<p>ASDP (Advanced Seismic Dissipator Project) è strutturato come un'applicazione web modulare che segue il pattern MVC (Model-View-Controller). L'applicazione è organizzata in cartelle specifiche per ogni tipo di funzionalità.</p>
<h2>Struttura delle Directory</h2>
<h3>Core Application</h3>
<ul>
<li><code>/</code> - Root directory contenente i file principali dell'applicazione
<ul>
<li><code>index.php</code> - Entry point dell'applicazione</li>
<li><code>home.php</code> - Dashboard principale dopo il login</li>
<li><code>.env</code> - File di configurazione dell'ambiente</li>
<li><code>.htaccess</code> - Configurazione Apache</li>
<li><code>composer.json</code> - Gestione dipendenze</li>
</ul></li>
</ul>
<h3>Moduli Principali</h3>
<ol>
<li>
<p><strong>Admin Module</strong> (<code>/admin</code>)</p>
<ul>
<li>Gestione utenti</li>
<li>Configurazioni sistema</li>
<li>Monitoraggio accessi</li>
<li>Gestione backup</li>
</ul>
</li>
<li>
<p><strong>API Module</strong> (<code>/api</code>)</p>
<ul>
<li><code>backup_process_zip.php</code> - Gestione backup (sistema ZIP)</li>
<li><code>delete_backup.php</code> - Eliminazione backup</li>
<li><code>get_backups.php</code> - Lista backup</li>
<li><code>download_backup.php</code> - Download backup</li>
<li><code>calculate_seismic_params.php</code> - Calcoli sismici</li>
<li><code>seismic_data.php</code> - Dati sismici</li>
<li><code>catasto_proxy.php</code> - Proxy catastale</li>
<li><code>cadastral_data.php</code> - Dati catastali</li>
<li><code>get_boundaries.php</code> - Confini geografici</li>
<li><code>get_stats.php</code> - Statistiche sistema</li>
</ul>
</li>
<li>
<p><strong>Authentication</strong> (<code>/auth</code>)</p>
<ul>
<li>Login/Logout</li>
<li>Registrazione</li>
<li>Recupero password</li>
<li>Gestione sessioni</li>
</ul>
</li>
<li>
<p><strong>Core Components</strong> (<code>/includes</code>)</p>
<ul>
<li><code>SeismicCalculator.php</code> - Engine calcoli sismici</li>
<li><code>ConfigManager.php</code> - Gestione configurazioni</li>
<li><code>db_config.php</code> - Database</li>
<li><code>GoogleMapsGeocoder.php</code> - Servizi di geocoding</li>
<li><code>logger.php</code> - Sistema logging</li>
</ul>
</li>
<li>
<p><strong>Frontend</strong> (<code>/js</code>, <code>/css</code>)</p>
<ul>
<li><strong>JavaScript Modules</strong> (<code>/js</code>)
<ul>
<li><code>ui.js</code> - Core UI</li>
<li><code>report.js</code> - Sistema report</li>
<li><code>map.js</code> - Gestione mappe</li>
<li><code>SeismicUI.js</code> - Interfaccia calcoli</li>
<li><code>SeismicCalculator.js</code> - Logica calcoli</li>
</ul></li>
<li><strong>CSS Styles</strong> (<code>/css</code>)
<ul>
<li>Stili personalizzati</li>
<li>Temi</li>
<li>Responsive design</li>
</ul></li>
</ul>
</li>
<li>
<p><strong>Modulo Massa Inerziale</strong> (<code>/inertial_mass</code>)</p>
<ul>
<li>Sistema a tre livelli LLM (Deepseek → Gemma3 → Locale)</li>
<li>Interfaccia modale responsive</li>
<li>API specializzate per calcoli massa inerziale</li>
<li>Cache intelligente per ottimizzazione</li>
</ul>
</li>
<li>
<p><strong>Resources</strong></p>
<ul>
<li><code>/img</code> - Immagini e icone</li>
<li><code>/docs</code> - Documentazione completa</li>
<li><code>/logs</code> - File di log (puliti)</li>
<li><code>/sql</code> - Script database</li>
<li><code>/cache</code> - Cache sistema</li>
<li><code>/backups</code> - Directory backup</li>
</ul>
</li>
<li>
<p><strong>Development Tools</strong></p>
<ul>
<li><code>/tools</code> - Strumenti sviluppo e utilità</li>
<li><code>/vendor</code> - Dipendenze esterne (Composer)</li>
</ul>
</li>
</ol>
<h2>Funzionalità Core</h2>
<h3>1. Sistema di Calcolo Sismico</h3>
<ul>
<li>Classe principale: <code>SeismicCalculator.php</code></li>
<li>Interfaccia: <code>SeismicUI.js</code></li>
<li>API: <code>calculate_seismic_params.php</code></li>
</ul>
<h3>2. Gestione Mappe</h3>
<ul>
<li>Core: <code>map.js</code></li>
<li>Geocoding: <code>GoogleMapsGeocoder.php</code></li>
<li>Dati catastali: <code>catasto_proxy.php</code></li>
</ul>
<h3>3. Sistema Report</h3>
<ul>
<li>Generazione: <code>report.js</code></li>
<li>Template HTML</li>
<li>Export PDF (in sviluppo)</li>
</ul>
<h3>4. Gestione Utenti</h3>
<ul>
<li>Autenticazione: <code>/auth</code></li>
<li>Profili: <code>account.js</code></li>
<li>Permessi: Admin module</li>
</ul>
<h2>Database Structure</h2>
<pre><code class="language-sql">-- Struttura principale
users/
  |- id
  |- username
  |- email
  |- password
  |- created_at

calculations/
  |- id
  |- user_id
  |- location_data
  |- seismic_params
  |- created_at

settings/
  |- id
  |- user_id
  |- key
  |- value

logs/
  |- id
  |- user_id
  |- action
  |- details
  |- created_at</code></pre>
<h2>Sicurezza</h2>
<ul>
<li>Autenticazione basata su sessioni</li>
<li>Password hashing (bcrypt)</li>
<li>Protezione XSS</li>
<li>Validazione input</li>
<li>Rate limiting</li>
<li>CSRF protection</li>
</ul>
<h2>Configurazione</h2>
<ul>
<li>File <code>.env</code> per variabili ambiente</li>
<li><code>ConfigManager.php</code> per gestione configurazioni</li>
<li>Override configurazioni in admin panel</li>
</ul>
<h2>Logging</h2>
<p>Sistema di logging multi-livello:</p>
<ul>
<li>Error logging</li>
<li>Access logging</li>
<li>Audit logging</li>
<li>Debug logging</li>
</ul>
<h2>Backup System</h2>
<ul>
<li>Backup database</li>
<li>Backup file</li>
<li>Backup configurazioni</li>
<li>Report backup</li>
</ul>
<h2>Development Workflow</h2>
<ol>
<li>Development locale (XAMPP)</li>
<li>Testing automatizzato</li>
<li>Code review</li>
<li>Deployment staging</li>
<li>Deployment production</li>
</ol>
<h2>Note Importanti</h2>
<ul>
<li>Mantenere aggiornato composer.json</li>
<li>Seguire le convenzioni di codice</li>
<li>Documentare le modifiche</li>
<li>Mantenere il logging</li>
<li>Consultare STRUTTURA_PROGETTO.md per dettagli architetturali</li>
</ul>
<h2>Pulizia Workspace (05/06/2025)</h2>
<p>La struttura è stata ottimizzata con la rimozione di:</p>
<ul>
<li>File obsoleti e duplicati</li>
<li>Directory di test non utilizzate (<code>/src</code>, <code>/tests</code>)</li>
<li>File di debug temporanei</li>
<li>Cache e log per ambiente pulito</li>
<li>Documentazione temporanea</li>
</ul>
<p>Questa pulizia ha migliorato:</p>
<ul>
<li><strong>Performance</strong>: Meno file da processare</li>
<li><strong>Manutenibilità</strong>: Struttura più chiara</li>
<li><strong>Navigabilità</strong>: Organizzazione ottimizzata</li>
<li><strong>Documentazione</strong>: Architettura completamente documentata</li>
</ul>
<h1>3. Componenti Principali</h1>
<h2>Backend (PHP)</h2>
<ul>
<li>
<p><strong>Architettura MVC</strong></p>
<ul>
<li>Model: Gestione dati e logica business</li>
<li>View: Template e presentazione</li>
<li>Controller: Gestione richieste e routing</li>
</ul>
</li>
<li>
<p><strong>Gestione Sessioni</strong></p>
<ul>
<li>Autenticazione utenti</li>
<li>Mantenimento stato</li>
<li>Sicurezza accessi</li>
<li>Timeout configurabile</li>
</ul>
</li>
<li>
<p><strong>Calcolo Parametri Sismici</strong></p>
<ul>
<li>Implementazione NTC 2018</li>
<li>Validazione input</li>
<li>Calcoli real-time</li>
<li>Gestione errori</li>
</ul>
</li>
<li>
<p><strong>Validazione Input/Output</strong></p>
<ul>
<li>Sanitizzazione dati</li>
<li>Controlli tipo</li>
<li>Limiti e range</li>
<li>Messaggi errore</li>
</ul>
</li>
<li>
<p><strong>Integrazione Catasto</strong></p>
<ul>
<li>Proxy WMS Agenzia Entrate</li>
<li>Gestione coordinate</li>
<li>Parsing dati catastali</li>
<li>Cache risultati</li>
</ul>
</li>
</ul>
<h2>Frontend</h2>
<ul>
<li>
<p><strong>Interfaccia Responsive</strong></p>
<ul>
<li>Layout adattivo</li>
<li>Mobile-first design</li>
<li>Breakpoint configurabili</li>
<li>Tema scuro/chiaro</li>
</ul>
</li>
<li>
<p><strong>Mappa Interattiva</strong></p>
<ul>
<li>Leaflet.js per rendering</li>
<li>Layer catastale WMS</li>
<li>Selezione particelle</li>
<li>Visualizzazione dati catastali</li>
<li>Layer multipli</li>
<li>Controlli personalizzati</li>
</ul>
</li>
<li>
<p><strong>Calcoli Real-time</strong></p>
<ul>
<li>AJAX per richieste</li>
<li>Validazione client-side</li>
<li>Feedback immediato</li>
<li>Cache risultati</li>
</ul>
</li>
<li>
<p><strong>PWA per Offline</strong></p>
<ul>
<li>Service Worker</li>
<li>Cache API</li>
<li>Manifest</li>
<li>Push notifications</li>
</ul>
</li>
</ul>
<h2>Sistema di Backup</h2>
<ul>
<li>
<p><strong>Backup Database Automatico</strong></p>
<ul>
<li>Schedule configurabile</li>
<li>Compressione dati</li>
<li>Rotazione file</li>
<li>Verifica integrità</li>
</ul>
</li>
<li>
<p><strong>Backup File Sorgente</strong></p>
<ul>
<li>Inclusione selettiva</li>
<li>Versionamento</li>
<li>Esclusione temp</li>
<li>Backup incrementale</li>
</ul>
</li>
<li>
<p><strong>Compressione ZIP</strong></p>
<ul>
<li>Algoritmo ottimizzato</li>
<li>Password protection</li>
<li>Split archivi</li>
<li>Verifica CRC</li>
</ul>
</li>
<li>
<p><strong>Log Operazioni</strong></p>
<ul>
<li>Tracciamento completo</li>
<li>Notifiche errori</li>
<li>Report periodici</li>
<li>Pulizia automatica </li>
</ul>
</li>
</ul>
<h1>4. Database</h1>
<h2>Struttura Database</h2>
<h3>Tabelle di Calcolo</h3>
<h4>CALCULATION_PARAMETERS</h4>
<pre><code class="language-sql">CREATE TABLE calculation_parameters (
    id INT PRIMARY KEY AUTO_INCREMENT,
    calculation_id INT,
    parameter_type VARCHAR(10),
    tr INT,
    ag DECIMAL(10,6),
    f0 DECIMAL(10,6),
    tc_star DECIMAL(10,6),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (calculation_id) REFERENCES calculation_log(id)
);</code></pre>
<h4>CALCULATION_SPECTRA</h4>
<pre><code class="language-sql">CREATE TABLE calculation_spectra (
    id INT PRIMARY KEY AUTO_INCREMENT,
    calculation_id INT,
    parameter_type VARCHAR(10),
    spectrum_data JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (calculation_id) REFERENCES calculation_log(id)
);</code></pre>
<h3>Tabelle Geografiche</h3>
<h4>SEISMIC_GRID_POINTS</h4>
<pre><code class="language-sql">CREATE TABLE seismic_grid_points (
    id INT PRIMARY KEY AUTO_INCREMENT,
    lat DECIMAL(10,6) NOT NULL,
    lng DECIMAL(10,6) NOT NULL,
    ag_value DECIMAL(10,6),
    f0_value DECIMAL(10,6),
    tc_star_value DECIMAL(10,6),
    UNIQUE KEY lat_lng (lat, lng)
);</code></pre>
<h4>ZONE_SISMICHE</h4>
<pre><code class="language-sql">CREATE TABLE zone_sismiche (
    id INT PRIMARY KEY AUTO_INCREMENT,
    zona VARCHAR(10) NOT NULL,
    descrizione TEXT,
    ag_min DECIMAL(10,6),
    ag_max DECIMAL(10,6)
);</code></pre>
<h4>CLASSIFICAZIONE_ZONE_SISMICHE</h4>
<pre><code class="language-sql">CREATE TABLE classificazione_zone_sismiche (
    id INT PRIMARY KEY AUTO_INCREMENT,
    comune_id INT NOT NULL,
    zona_id INT NOT NULL,
    data_aggiornamento DATE,
    KEY comune_id (comune_id),
    KEY zona_id (zona_id)
);</code></pre>
<h4>COMUNI</h4>
<pre><code class="language-sql">CREATE TABLE comuni (
    id INT PRIMARY KEY AUTO_INCREMENT,
    nome VARCHAR(100) NOT NULL,
    provincia VARCHAR(2) NOT NULL,
    regione VARCHAR(50) NOT NULL,
    lat DECIMAL(10,6),
    lng DECIMAL(10,6),
    UNIQUE KEY nome_provincia (nome, provincia)
);</code></pre>
<h4>CATASTO_INFO</h4>
<pre><code class="language-sql">CREATE TABLE catasto_info (
    id INT PRIMARY KEY AUTO_INCREMENT,
    comune_id INT NOT NULL,
    foglio VARCHAR(10) NOT NULL,
    particella VARCHAR(10) NOT NULL,
    sub VARCHAR(10),
    lat DECIMAL(10,6),
    lng DECIMAL(10,6),
    KEY comune_id (comune_id),
    FOREIGN KEY (comune_id) REFERENCES comuni(id)
);</code></pre>
<h3>Tabelle Utenti</h3>
<h4>PASSWORD_RESETS</h4>
<pre><code class="language-sql">CREATE TABLE password_resets (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    token VARCHAR(100) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP NOT NULL,
    used TINYINT(1) DEFAULT 0,
    KEY user_id (user_id),
    UNIQUE KEY token (token),
    FOREIGN KEY (user_id) REFERENCES users(id)
);</code></pre>
<h4>USER_SETTINGS</h4>
<pre><code class="language-sql">CREATE TABLE user_settings (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    setting_key VARCHAR(50) NOT NULL,
    setting_value TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY user_setting (user_id, setting_key),
    FOREIGN KEY (user_id) REFERENCES users(id)
);</code></pre>
<h2>Relazioni</h2>
<h3>Relazioni Principali</h3>
<ol>
<li>
<p><strong>CALCULATION_PARAMETERS → CALCULATION_LOG</strong></p>
<ul>
<li><code>calculation_parameters.calculation_id</code> → <code>calculation_log.id</code></li>
<li>Tipo: One-to-Many</li>
<li>Ogni calcolo può avere più parametri (SLO, SLD, etc.)</li>
</ul>
</li>
<li>
<p><strong>CALCULATION_SPECTRA → CALCULATION_LOG</strong></p>
<ul>
<li><code>calculation_spectra.calculation_id</code> → <code>calculation_log.id</code></li>
<li>Tipo: One-to-Many</li>
<li>Ogni calcolo può avere più spettri (SLO, SLD, etc.)</li>
</ul>
</li>
<li>
<p><strong>CATASTO_INFO → COMUNI</strong></p>
<ul>
<li><code>catasto_info.comune_id</code> → <code>comuni.id</code></li>
<li>Tipo: One-to-Many</li>
<li>Ogni record catastale appartiene a un comune</li>
</ul>
</li>
<li>
<p><strong>CLASSIFICAZIONE_ZONE_SISMICHE → COMUNI</strong></p>
<ul>
<li><code>classificazione_zone_sismiche.comune_id</code> → <code>comuni.id</code></li>
<li>Tipo: One-to-Many</li>
<li>Ogni comune può avere più classificazioni nel tempo</li>
</ul>
</li>
<li>
<p><strong>USER_SETTINGS → USERS</strong></p>
<ul>
<li><code>user_settings.user_id</code> → <code>users.id</code></li>
<li>Tipo: One-to-Many</li>
<li>Ogni utente può avere multiple impostazioni</li>
</ul>
</li>
</ol>
<h3>Indici</h3>
<ol>
<li>
<p><strong>CALCULATION_PARAMETERS</strong></p>
<ul>
<li><code>idx_calculation_id</code> su <code>calculation_id</code></li>
<li><code>idx_parameter_type</code> su <code>parameter_type</code></li>
</ul>
</li>
<li>
<p><strong>CALCULATION_SPECTRA</strong></p>
<ul>
<li><code>idx_calculation_id</code> su <code>calculation_id</code></li>
<li><code>idx_parameter_type</code> su <code>parameter_type</code></li>
</ul>
</li>
<li>
<p><strong>SEISMIC_GRID_POINTS</strong></p>
<ul>
<li><code>idx_lat_lng</code> su (<code>lat</code>, <code>lng</code>)</li>
</ul>
</li>
<li>
<p><strong>COMUNI</strong></p>
<ul>
<li><code>idx_nome_provincia</code> su (<code>nome</code>, <code>provincia</code>)</li>
<li><code>idx_coordinates</code> su (<code>lat</code>, <code>lng</code>)</li>
</ul>
</li>
<li>
<p><strong>CATASTO_INFO</strong></p>
<ul>
<li><code>idx_comune_id</code> su <code>comune_id</code></li>
<li><code>idx_foglio_part</code> su (<code>foglio</code>, <code>particella</code>)</li>
</ul>
</li>
</ol>
<h3>Vincoli</h3>
<ol>
<li>
<p><strong>Integrità Referenziale</strong></p>
<ul>
<li>ON DELETE: RESTRICT per tutte le foreign key</li>
<li>ON UPDATE: CASCADE per tutte le foreign key</li>
</ul>
</li>
<li>
<p><strong>Unicità</strong></p>
<ul>
<li>Token reset password</li>
<li>Coordinate griglia sismica</li>
<li>Nome e provincia dei comuni</li>
<li>Impostazioni utente</li>
</ul>
</li>
<li>
<p><strong>Not Null</strong></p>
<ul>
<li>Campi chiave primaria</li>
<li>Coordinate griglia sismica</li>
<li>Dati comuni (nome, provincia, regione)</li>
<li>Dati catastali (foglio, particella)</li>
</ul>
</li>
</ol>
<h2>Ottimizzazioni</h2>
<h3>1. Indici</h3>
<pre><code class="language-sql">-- Ottimizzazione ricerca parametri
CREATE INDEX idx_calc_params ON calculation_parameters (calculation_id, parameter_type);

-- Ottimizzazione ricerca spettri
CREATE INDEX idx_calc_spectra ON calculation_spectra (calculation_id, parameter_type);

-- Ottimizzazione ricerca geografica
CREATE INDEX idx_coordinates ON comuni (lat, lng);
CREATE INDEX idx_catasto_search ON catasto_info (comune_id, foglio, particella);</code></pre>
<h3>2. Partitioning</h3>
<pre><code class="language-sql">-- Partitioning per data sui calcoli
ALTER TABLE calculation_parameters
PARTITION BY RANGE (TO_DAYS(created_at)) (
    PARTITION p_2024_01 VALUES LESS THAN (TO_DAYS('2024-02-01')),
    PARTITION p_2024_02 VALUES LESS THAN (TO_DAYS('2024-03-01')),
    PARTITION p_future VALUES LESS THAN MAXVALUE
);</code></pre>
<h3>3. Manutenzione</h3>
<pre><code class="language-sql">-- Pulizia dati vecchi
CREATE EVENT clean_old_calculations
ON SCHEDULE EVERY 1 MONTH
DO
    DELETE FROM calculation_parameters 
    WHERE created_at &lt; DATE_SUB(NOW(), INTERVAL 1 YEAR);</code></pre>
<h2>Piano di Backup</h2>
<h3>1. Backup Giornaliero</h3>
<pre><code class="language-bash">mysqldump -u root asdp_db &gt; /backups/daily/asdp_$(date +%Y%m%d).sql</code></pre>
<h3>2. Backup Incrementale</h3>
<pre><code class="language-bash">mysqlbinlog --start-datetime="2024-01-01 00:00:00" \
            --stop-datetime="2024-01-02 00:00:00" \
            /var/log/mysql/mysql-bin.* &gt; /backups/incremental/backup.sql</code></pre>
<h3>3. Restore</h3>
<pre><code class="language-bash">mysql -u root asdp_db &lt; /backups/daily/asdp_20240115.sql</code></pre>
<h1>Documentazione API</h1>
<p>Ultimo aggiornamento: 20/01/2024</p>
<h2>Endpoint Principali</h2>
<h3>Calcolo Parametri Sismici</h3>
<pre><code class="language-http">POST /api/calculate_seismic_params
Content-Type: application/json

{
    "lat": number,          // Latitudine
    "lng": number,          // Longitudine
    "nominal_life": number, // Vita nominale
    "use_class": string,    // Classe d'uso
    "soil_category": string,// Categoria sottosuolo
    "topographic_category": string // Categoria topografica
}</code></pre>
<h3>Risposta</h3>
<pre><code class="language-json">{
    "success": boolean,
    "data": {
        "ag": number,      // Accelerazione orizzontale massima
        "F0": number,      // Fattore amplificazione spettrale
        "TC": number,      // Periodo inizio tratto velocità costante
        "SS": number,      // Coefficiente amplificazione stratigrafica
        "ST": number,      // Coefficiente amplificazione topografica
        "S": number,       // Coefficiente che tiene conto categoria sottosuolo
        "spectrum": {
            "elastic": [...], // Spettro elastico
            "design": [...]  // Spettro di progetto
        }
    }
}</code></pre>
<h3>Gestione Backup</h3>
<pre><code class="language-http">POST /api/backup/create
Content-Type: application/json

{
    "type": string,     // "full" o "partial"
    "description": string
}</code></pre>
<h3>Sistema Cache</h3>
<pre><code class="language-http">POST /api/cache/clear
GET /api/cache/status
DELETE /api/cache/{key}</code></pre>
<h2>Autenticazione</h2>
<ul>
<li>Tutte le richieste richiedono un token JWT valido</li>
<li>Il token va incluso nell'header Authorization</li>
<li>Formato: <code>Authorization: Bearer {token}</code></li>
</ul>
<h2>Rate Limiting</h2>
<ul>
<li>100 richieste/minuto per IP</li>
<li>1000 richieste/giorno per utente</li>
<li>Header risposta: X-RateLimit-*</li>
</ul>
<h2>Gestione Errori</h2>
<h3>Codici di Errore</h3>
<ul>
<li>400: Parametri mancanti o invalidi</li>
<li>401: Non autorizzato</li>
<li>403: Accesso negato</li>
<li>404: Risorsa non trovata</li>
<li>429: Troppe richieste</li>
<li>500: Errore interno server</li>
</ul>
<h3>Formato Errori</h3>
<pre><code class="language-json">{
    "success": false,
    "error": {
        "code": number,
        "message": string,
        "details": object
    }
}</code></pre>
<h2>Versioning</h2>
<ul>
<li>Versione corrente: v1</li>
<li>Base URL: <code>/api/v1/</code></li>
<li>Deprecation notice via header</li>
</ul>
<h2>Best Practices</h2>
<ol>
<li>Utilizzare HTTPS</li>
<li>Implementare retry con exponential backoff</li>
<li>Cachare le risposte quando possibile</li>
<li>Gestire tutti gli errori</li>
<li>Validare input lato client</li>
</ol>
<h2>Note Tecniche</h2>
<ul>
<li>Content-Type: application/json</li>
<li>Charset: UTF-8</li>
<li>Timezone: UTC</li>
<li>Date format: ISO 8601</li>
</ul>
<h2>Esempi</h2>
<h3>Calcolo Sismico Base</h3>
<pre><code class="language-javascript">fetch('/api/v1/calculate_seismic_params', {
    method: 'POST',
    headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer ' + token
    },
    body: JSON.stringify({
        lat: 41.902782,
        lng: 12.496366,
        nominal_life: 50,
        use_class: "II",
        soil_category: "B",
        topographic_category: "T1"
    })
})</code></pre>
<h2>Changelog API</h2>
<h3>v1.1.0 (20/01/2024)</h3>
<ul>
<li>Aggiunto endpoint gestione backup</li>
<li>Implementato sistema cache</li>
<li>Migliorato rate limiting</li>
<li>Aggiunta documentazione errori</li>
</ul>
<h3>v1.0.9 (15/01/2024)</h3>
<ul>
<li>Ottimizzato calcolo sismico</li>
<li>Aggiunto sistema report</li>
<li>Migliorata gestione errori</li>
</ul>
<h2>Endpoint Disponibili</h2>
<h3>1. Calcolo Parametri Sismici</h3>
<pre><code>POST /api/calculate_seismic_params.php

Request:
{
    "lat": number,          // Latitudine del punto
    "lng": number,          // Longitudine del punto
    "nominalLife": number,  // Vita nominale (anni)
    "buildingClass": string,// Classe edificio (I, II, III, IV)
    "soilCategory": string, // Categoria terreno (A, B, C, D, E)
    "topographicCategory": string // Categoria topografica (T1, T2, T3, T4)
}

Response:
{
    "success": boolean,
    "data": {
        "SLO": {
            "TR": number,   // Tempo di ritorno
            "ag": number,   // Accelerazione al suolo
            "F0": number,   // Fattore amplificazione
            "TC": number    // Periodo TC*
        },
        "SLD": {...},
        "SLV": {...},
        "SLC": {...}
    },
    "message": string      // Messaggio di errore (se success = false)
}

### Endpoint: `/api/cadastral_data.php`

#### Richiesta
```http
POST /api/cadastral_data.php
Content-Type: application/json

{
    "lat": 41.809926,
    "lng": 12.613875
}</code></pre>
<h4>Parametri</h4>
<ul>
<li><strong>lat</strong> (float): Latitudine del punto</li>
<li><strong>lng</strong> (float): Longitudine del punto</li>
</ul>
<h4>Risposta</h4>
<pre><code class="language-json">{
    "comune": "H501",
    "sezione": "C", 
    "foglio": "1012",
    "allegato": "B",
    "sviluppo": "0",
    "particella": "1936",
    "codice_completo": "H501C1012B0.1936"
}</code></pre>
<h4>Note</h4>
<ul>
<li>L'endpoint utilizza il servizio WMS dell'Agenzia delle Entrate</li>
<li>Le coordinate devono essere nel sistema WGS84 (EPSG:4326)</li>
<li>Il servizio restituisce i dati catastali della particella selezionata</li>
<li>In caso di errore o particella non trovata, i campi avranno valore &quot;-&quot;</li>
</ul>
<h3>Endpoint: <code>/api/catasto_proxy.php</code></h3>
<h4>Richiesta</h4>
<pre><code class="language-http">GET /api/catasto_proxy.php?SERVICE=WMS&amp;VERSION=1.3.0&amp;REQUEST=GetFeatureInfo&amp;...</code></pre>
<h4>Parametri</h4>
<ul>
<li>Tutti i parametri standard del servizio WMS GetFeatureInfo</li>
<li>Le coordinate devono essere nel sistema EPSG:4258 (ETRS89)</li>
</ul>
<h4>Risposta</h4>
<ul>
<li>Formato HTML con i dati catastali come restituiti dal servizio WMS</li>
<li>In caso di errore, viene restituito un messaggio di errore appropriato</li>
</ul>
<h3>Endpoint: <code>/api/get_boundaries.php</code></h3>
<h4>Richiesta</h4>
<pre><code class="language-http">GET /api/get_boundaries.php?comune=Roma</code></pre>
<h4>Parametri</h4>
<ul>
<li><strong>comune</strong> (string): Nome del comune</li>
</ul>
<h4>Risposta</h4>
<pre><code class="language-json">{
    "success": true,
    "data": {
        "type": "Feature",
        "properties": {
            "nome": "Roma",
            "provincia": "RM",
            "regione": "Lazio"
        },
        "geometry": {
            "type": "Polygon",
            "coordinates": [
                [
                    [12.234, 41.789],
                    [12.567, 41.923],
                    [12.789, 41.856],
                    [12.234, 41.789]
                ]
            ]
        }
    }
}</code></pre>
<h2>Note Tecniche</h2>
<h3>Autenticazione</h3>
<ul>
<li>Tutte le API richiedono un token JWT valido</li>
<li>Il token va passato nell'header <code>Authorization</code></li>
<li>Formato: <code>Bearer &lt;token&gt;</code></li>
</ul>
<h3>Rate Limiting</h3>
<ul>
<li>Max 100 richieste/minuto per IP</li>
<li>Max 1000 richieste/ora per utente</li>
<li>Status 429 se limite superato</li>
</ul>
<h3>Caching</h3>
<ul>
<li>Risultati cachati per 1 ora</li>
<li>Header <code>Cache-Control</code> gestito automaticamente</li>
<li>Possibilità di forzare refresh</li>
</ul>
<h3>CORS</h3>
<ul>
<li>Abilitato per domini autorizzati</li>
<li>Metodi permessi: GET, POST</li>
<li>Credenziali accettate </li>
</ul>
<h1>Analisi Normative Calcolo Sismico</h1>
<h2>Documenti di Riferimento</h2>
<ol>
<li>Azione sismica 2008.pdf</li>
<li>2008_D_Min_Infrastrutture_14-01-NTC-Allegati.pdf</li>
<li>allegati.pdf</li>
</ol>
<h2>Obiettivi dell'Analisi</h2>
<ol>
<li>Verificare le formule di calcolo ufficiali</li>
<li>Controllare i metodi di interpolazione</li>
<li>Validare i coefficienti utilizzati</li>
<li>Assicurare la conformità con la normativa</li>
</ol>
<h2>Struttura dell'Analisi</h2>
<p>Per ogni documento analizzeremo:</p>
<ol>
<li>
<p><strong>Formule di Calcolo</strong></p>
<ul>
<li>Equazioni fondamentali</li>
<li>Coefficienti e parametri</li>
<li>Metodi di interpolazione</li>
<li>Limiti e condizioni</li>
</ul>
</li>
<li>
<p><strong>Parametri di Input</strong></p>
<ul>
<li>Coordinate geografiche</li>
<li>Caratteristiche del sito</li>
<li>Parametri strutturali</li>
<li>Coefficienti di amplificazione</li>
</ul>
</li>
<li>
<p><strong>Procedure di Calcolo</strong></p>
<ul>
<li>Sequenza operazioni</li>
<li>Validazioni intermedie</li>
<li>Controlli di coerenza</li>
<li>Arrotondamenti</li>
</ul>
</li>
<li>
<p><strong>Output Attesi</strong></p>
<ul>
<li>Formato risultati</li>
<li>Precisione richiesta</li>
<li>Verifiche di validità</li>
<li>Limiti accettabili</li>
</ul>
</li>
</ol>
<h2>Analisi Documento 1: Azione sismica 2008.pdf</h2>
<h3>1. Metodo di Interpolazione</h3>
<p>Il documento specifica che l'interpolazione dei valori deve essere effettuata secondo la seguente procedura:</p>
<ol>
<li>
<p><strong>Identificazione Punti Griglia</strong>:</p>
<ul>
<li>Individuare i 4 punti della griglia più vicini al punto di interesse</li>
<li>Verificare che il punto sia all'interno del quadrilatero formato dai 4 punti</li>
</ul>
</li>
<li>
<p><strong>Formula di Interpolazione</strong>:</p>
<pre><code>p = p1 * (1-x) * (1-y) + p2 * x * (1-y) + p3 * (1-x) * y + p4 * x * y</code></pre>
<p>dove:</p>
<ul>
<li>p è il valore nel punto di interesse</li>
<li>p1, p2, p3, p4 sono i valori nei punti della griglia</li>
<li>x, y sono le distanze normalizzate (tra 0 e 1)</li>
</ul>
</li>
<li>
<p><strong>Parametri da Interpolare</strong>:</p>
<ul>
<li>ag (accelerazione orizzontale massima)</li>
<li>F0 (fattore di amplificazione spettrale massima)</li>
<li>TC* (periodo di inizio del tratto a velocità costante)</li>
</ul>
</li>
</ol>
<h3>2. Calcolo Periodo di Ritorno (TR)</h3>
<p>Il periodo di ritorno TR viene calcolato come:</p>
<pre><code>TR = -VR / ln(1-PVR)</code></pre>
<p>dove:</p>
<ul>
<li>VR = VN * CU (periodo di riferimento)</li>
<li>VN = vita nominale</li>
<li>CU = coefficiente d'uso</li>
<li>PVR = probabilità di superamento nel periodo di riferimento</li>
</ul>
<h3>3. Stati Limite e Probabilità</h3>
<p>Stati Limite di Esercizio (SLE):</p>
<ul>
<li>SLO: PVR = 81%</li>
<li>SLD: PVR = 63%</li>
</ul>
<p>Stati Limite Ultimi (SLU):</p>
<ul>
<li>SLV: PVR = 10%</li>
<li>SLC: PVR = 5%</li>
</ul>
<h2>Analisi Documento 2: 2008_D_Min_Infrastrutture_14-01-NTC-Allegati.pdf</h2>
<h3>1. Categorie di Sottosuolo</h3>
<p>Il documento definisce 5 categorie di sottosuolo principali:</p>
<p>A. Ammassi rocciosi o terreni molto rigidi<br />
B. Rocce tenere e depositi di terreni a grana grossa molto addensati<br />
C. Depositi di terreni a grana grossa mediamente addensati<br />
D. Depositi di terreni a grana grossa scarsamente addensati<br />
E. Terreni con caratteristiche meccaniche particolarmente scadenti</p>
<h3>2. Coefficienti di Amplificazione Stratigrafica (SS e CC)</h3>
<p>Per ogni categoria di sottosuolo, si applicano i seguenti coefficienti:</p>
<p><strong>Categoria A:</strong></p>
<ul>
<li>SS = 1.00</li>
<li>CC = 1.00</li>
</ul>
<p><strong>Categoria B:</strong></p>
<ul>
<li>SS = 1.00 ≤ 1.40 - 0.40 <em> F0 </em> ag/g ≤ 1.20</li>
<li>CC = 1.10 <em> (TC</em>)-0.20</li>
</ul>
<p><strong>Categoria C:</strong></p>
<ul>
<li>SS = 1.00 ≤ 1.70 - 0.60 <em> F0 </em> ag/g ≤ 1.50</li>
<li>CC = 1.05 <em> (TC</em>)-0.33</li>
</ul>
<p><strong>Categoria D:</strong></p>
<ul>
<li>SS = 0.90 ≤ 2.40 - 1.50 <em> F0 </em> ag/g ≤ 1.80</li>
<li>CC = 1.25 <em> (TC</em>)-0.50</li>
</ul>
<p><strong>Categoria E:</strong></p>
<ul>
<li>SS = 1.00 ≤ 2.00 - 1.10 <em> F0 </em> ag/g ≤ 1.60</li>
<li>CC = 1.15 <em> (TC</em>)-0.40</li>
</ul>
<h3>3. Coefficienti Topografici (ST)</h3>
<p>Categorie topografiche e relativi coefficienti:</p>
<p>T1. ST = 1.0 (superficie pianeggiante)<br />
T2. ST = 1.2 (pendii con inclinazione &gt; 15°)<br />
T3. ST = 1.2 (rilievi con larghezza cresta &lt; altezza)<br />
T4. ST = 1.4 (rilievi con larghezza cresta molto minore dell'altezza)</p>
<h3>4. Calcolo dei Periodi TC e TB</h3>
<p>Il periodo TC è espresso in secondi e viene calcolato come:</p>
<pre><code>TC = CC * TC*</code></pre>
<p>dove:</p>
<ul>
<li>TC* è il periodo di riferimento (da interpolazione) espresso in secondi</li>
<li>CC è il coefficiente di categoria del sottosuolo (adimensionale)</li>
</ul>
<p>Il periodo TB, anch'esso in secondi, si calcola come:</p>
<pre><code>TB = TC / 3</code></pre>
<p>IMPORTANTE:</p>
<ol>
<li>
<p>TC* viene ottenuto per interpolazione dai valori della griglia di riferimento</p>
</li>
<li>
<p>CC dipende dalla categoria di sottosuolo secondo le formule:</p>
<ul>
<li>Categoria A: CC = 1.00</li>
<li>Categoria B: CC = 1.10 <em> (TC</em>)^(-0.20)</li>
<li>Categoria C: CC = 1.05 <em> (TC</em>)^(-0.33)</li>
<li>Categoria D: CC = 1.25 <em> (TC</em>)^(-0.50)</li>
<li>Categoria E: CC = 1.15 <em> (TC</em>)^(-0.40)</li>
</ul>
</li>
<li>
<p>I valori di TC e TB devono essere arrotondati a 3 decimali</p>
</li>
<li>
<p>TC e TB non possono essere negativi o nulli</p>
</li>
<li>
<p>TC deve essere sempre maggiore di TB</p>
</li>
</ol>
<p>Esempio di calcolo per categoria B:</p>
<ul>
<li>TC* = 0.306 s (da interpolazione)</li>
<li>CC = 1.10 * (0.306)^(-0.20) = 1.357</li>
<li>TC = 1.357 * 0.306 = 0.415 s</li>
<li>TB = 0.415 / 3 = 0.138 s</li>
</ul>
<h2>Analisi Documento 3: allegati.pdf</h2>
<h3>1. Spettro di Risposta Elastico in Accelerazione</h3>
<p>Lo spettro di risposta elastico in accelerazione è definito dalle seguenti espressioni:</p>
<ol>
<li>
<p><strong>Per 0 ≤ T &lt; TB</strong>:</p>
<pre><code>Se(T) = ag * S * η * F0 * [(T/TB) + (1/(η*F0)) * (1 - T/TB)]</code></pre>
</li>
<li>
<p><strong>Per TB ≤ T &lt; TC</strong>:</p>
<pre><code>Se(T) = ag * S * η * F0</code></pre>
</li>
<li>
<p><strong>Per TC ≤ T &lt; TD</strong>:</p>
<pre><code>Se(T) = ag * S * η * F0 * (TC/T)</code></pre>
</li>
<li>
<p><strong>Per TD ≤ T</strong>:</p>
<pre><code>Se(T) = ag * S * η * F0 * (TC*TD/T²)</code></pre>
</li>
</ol>
<p>dove:</p>
<ul>
<li>S = SS * ST (coefficiente che tiene conto della categoria di sottosuolo e delle condizioni topografiche)</li>
<li>η = √(10/(5+ξ)) ≥ 0.55 (fattore che altera lo spettro elastico per coefficienti di smorzamento viscosi ξ diversi dal 5%)</li>
<li>T = periodo di vibrazione</li>
<li>F0 = fattore che quantifica l'amplificazione spettrale massima</li>
<li>TC = CC <em> TC</em> (periodo corrispondente all'inizio del tratto a velocità costante)</li>
<li>TB = TC/3 (periodo corrispondente all'inizio del tratto ad accelerazione costante)</li>
<li>TD = 4.0 * (ag/g) + 1.6 (periodo corrispondente all'inizio del tratto a spostamento costante)</li>
</ul>
<h3>2. Fattore di Struttura (q)</h3>
<p>Il fattore di struttura q da utilizzare per ciascuno stato limite è:</p>
<ul>
<li>
<p><strong>Stati Limite di Esercizio (SLE)</strong>:</p>
<ul>
<li>SLO: q = 1</li>
<li>SLD: q = 1</li>
</ul>
</li>
<li>
<p><strong>Stati Limite Ultimi (SLU)</strong>:</p>
<ul>
<li>SLV: q &gt; 1 (dipende dalla tipologia strutturale)</li>
<li>SLC: q &gt; 1 (dipende dalla tipologia strutturale)</li>
</ul>
</li>
</ul>
<p>Il valore di q dipende da:</p>
<ol>
<li>Materiale strutturale (CA, CAP, Acciaio, Legno, Muratura)</li>
<li>Tipologia strutturale</li>
<li>Regolarità in pianta e in altezza</li>
<li>Classe di duttilità</li>
</ol>
<h3>3. Spettro di Progetto</h3>
<p>Lo spettro di progetto Sd(T) si ottiene dallo spettro elastico sostituendo η con 1/q:</p>
<pre><code>Sd(T) = Se(T) / q</code></pre>
<h2>Stato Analisi</h2>
<ul>
<li>[x] Azione sismica 2008.pdf</li>
<li>[x] 2008_D_Min_Infrastrutture_14-01-NTC-Allegati.pdf</li>
<li>[x] allegati.pdf</li>
</ul>
<h2>Note e Osservazioni</h2>
<ol>
<li>
<p><strong>Differenze Riscontrate</strong>:</p>
<ul>
<li>Il nostro codice attuale non implementa correttamente la formula di interpolazione</li>
<li>I periodi di ritorno calcolati sembrano corretti</li>
<li>La precisione dei risultati è conforme (3 decimali)</li>
</ul>
</li>
<li>
<p><strong>Azioni Necessarie</strong>:</p>
<ul>
<li>Correggere la formula di interpolazione</li>
<li>Aggiungere validazioni sui limiti geografici</li>
<li>Implementare controlli di coerenza sui risultati</li>
</ul>
</li>
<li>
<p><strong>Nuove Differenze Riscontrate</strong>:</p>
<ul>
<li>I coefficienti di amplificazione stratigrafica devono rispettare limiti precisi</li>
<li>Il calcolo di TC e TB deve considerare il coefficiente CC</li>
<li>I coefficienti topografici devono essere applicati correttamente</li>
</ul>
</li>
<li>
<p><strong>Ulteriori Azioni Necessarie</strong>:</p>
<ul>
<li>Implementare i controlli sui limiti dei coefficienti SS</li>
<li>Aggiungere il calcolo corretto di TC e TB</li>
<li>Verificare l'applicazione dei coefficienti topografici</li>
</ul>
</li>
<li>
<p><strong>Ulteriori Differenze Riscontrate</strong>:</p>
<ul>
<li>Il calcolo dello smorzamento η non è implementato</li>
<li>Il fattore di struttura q non è considerato</li>
<li>Lo spettro di progetto non è calcolato</li>
</ul>
</li>
<li>
<p><strong>Azioni Finali Necessarie</strong>:</p>
<ul>
<li>Implementare il calcolo completo dello spettro elastico</li>
<li>Aggiungere il calcolo del fattore η per diversi smorzamenti</li>
<li>Implementare il calcolo dello spettro di progetto con fattore q</li>
<li>Aggiungere validazioni per tutti i parametri di input</li>
</ul>
</li>
</ol>
<p><em>L'analisi di tutti i documenti è completata. Possiamo procedere con l'implementazione delle correzioni necessarie.</em> </p>
<h1>Procedure Operative</h1>
<p>Ultimo aggiornamento: 20/01/2024</p>
<h2>1. Gestione Sistema</h2>
<h3>1.1 Avvio Sistema</h3>
<ol>
<li>Verifica servizi attivi (Apache, MySQL)</li>
<li>Controllo file di configurazione</li>
<li>Verifica connessione database</li>
<li>Test connettività API esterne</li>
</ol>
<h3>1.2 Backup Sistema</h3>
<ol>
<li>Backup automatico giornaliero</li>
<li>Backup manuale su richiesta</li>
<li>Verifica integrità backup</li>
<li>Gestione retention policy</li>
</ol>
<h3>1.3 Logging</h3>
<ol>
<li>Monitoraggio log applicativi</li>
<li>Rotazione log automatica</li>
<li>Analisi errori critici</li>
<li>Notifiche anomalie</li>
</ol>
<h2>2. Gestione Utenti</h2>
<h3>2.1 Registrazione</h3>
<ol>
<li>Validazione dati utente</li>
<li>Verifica email</li>
<li>Assegnazione ruolo</li>
<li>Notifica conferma</li>
</ol>
<h3>2.2 Gestione Account</h3>
<ol>
<li>Modifica profilo</li>
<li>Cambio password</li>
<li>Reset password</li>
<li>Disattivazione account</li>
</ol>
<h2>3. Calcolo Sismico</h2>
<h3>3.1 Input Dati</h3>
<ol>
<li>Inserimento coordinate</li>
<li>Selezione parametri</li>
<li>Validazione input</li>
<li>Calcolo preliminare</li>
</ol>
<h3>3.2 Elaborazione</h3>
<ol>
<li>Calcolo parametri sismici</li>
<li>Generazione spettri</li>
<li>Validazione risultati</li>
<li>Creazione report</li>
</ol>
<h2>4. Manutenzione</h2>
<h3>4.1 Database</h3>
<ol>
<li>Ottimizzazione tabelle</li>
<li>Pulizia dati obsoleti</li>
<li>Verifica integrità</li>
<li>Backup periodici</li>
</ol>
<h3>4.2 Cache</h3>
<ol>
<li>Gestione cache applicativa</li>
<li>Pulizia cache obsoleta</li>
<li>Ottimizzazione performance</li>
<li>Monitoraggio utilizzo</li>
</ol>
<h3>4.3 File System</h3>
<ol>
<li>Pulizia file temporanei</li>
<li>Gestione spazio disco</li>
<li>Backup documenti</li>
<li>Organizzazione directory</li>
</ol>
<h2>5. Monitoraggio</h2>
<h3>5.1 Performance</h3>
<ol>
<li>Monitoraggio CPU/RAM</li>
<li>Analisi tempi risposta</li>
<li>Ottimizzazione query</li>
<li>Cache hit ratio</li>
</ol>
<h3>5.2 Sicurezza</h3>
<ol>
<li>Controllo accessi</li>
<li>Analisi log sicurezza</li>
<li>Verifica permessi</li>
<li>Scan vulnerabilità</li>
</ol>
<h2>Note Operative</h2>
<ol>
<li>Seguire le procedure in ordine</li>
<li>Documentare ogni intervento</li>
<li>Verificare risultati</li>
<li>Segnalare anomalie </li>
</ol>
<h1>7. Troubleshooting</h1>
<h2>Errori Comuni</h2>
<h3>1. Errori di Connessione Database</h3>
<pre><code>Error: SQLSTATE[HY000] [1045] Access denied for user 'asdp_user'@'localhost'</code></pre>
<p><strong>Soluzioni:</strong></p>
<ol>
<li>Verificare credenziali in <code>.env</code></li>
<li>Controllare utente MySQL:
<pre><code class="language-sql">SELECT User, Host FROM mysql.user;</code></pre></li>
<li>Ricreare utente:
<pre><code class="language-sql">DROP USER 'asdp_user'@'localhost';
CREATE USER 'asdp_user'@'localhost' IDENTIFIED BY 'password';
GRANT ALL PRIVILEGES ON asdp_db.* TO 'asdp_user'@'localhost';
FLUSH PRIVILEGES;</code></pre></li>
</ol>
<h3>2. Errori Mappa</h3>
<pre><code class="language-javascript">Error: Leaflet - Tiles not loading</code></pre>
<p><strong>Soluzioni:</strong></p>
<ol>
<li>Verificare connessione internet</li>
<li>Controllare API key in <code>js/map.js</code></li>
<li>Pulire cache browser:
<pre><code class="language-powershell"># Eliminare cache
del /S /Q "%LocalAppData%\Google\Chrome\User Data\Default\Cache\*"</code></pre></li>
<li>Verificare console browser (F12)</li>
</ol>
<h3>3. Errori Calcolo Sismico</h3>
<pre><code>Error: Invalid coordinates or parameters</code></pre>
<p><strong>Soluzioni:</strong></p>
<ol>
<li>
<p>Verificare formato coordinate:</p>
<ul>
<li>Latitudine: -90 a +90</li>
<li>Longitudine: -180 a +180</li>
</ul>
</li>
<li>
<p>Controllare parametri:</p>
<pre><code class="language-php">// Valori ammessi
$vitaNominale = [50, 100];
$classeUso = ['I', 'II', 'III', 'IV'];
$categoriaTerreno = ['A', 'B', 'C', 'D', 'E'];</code></pre>
</li>
</ol>
<h3>4. Errori Permessi File</h3>
<pre><code>Warning: Permission denied in /logs/error.log</code></pre>
<p><strong>Soluzioni:</strong></p>
<ol>
<li>
<p>Verificare permessi cartelle:</p>
<pre><code class="language-powershell"># Visualizza permessi
icacls "C:\xampp\htdocs\asdp\logs"

# Imposta permessi
icacls "C:\xampp\htdocs\asdp\logs" /grant Users:(OI)(CI)F</code></pre>
</li>
<li>
<p>Controllare proprietario:</p>
<pre><code class="language-powershell"># Cambia proprietario
takeown /F "C:\xampp\htdocs\asdp\logs" /R</code></pre>
</li>
</ol>
<h2>Problemi frequenti e soluzioni</h2>
<ul>
<li><strong>Dati obsoleti o errori inspiegabili</strong>: Se il sistema mostra errori strani dopo aggiornamenti o import di database, eseguire una pulizia completa di database (svuotamento tabelle temporanee, log, backup) e rimuovere tutti i file temporanei/log dalle cartelle <code>backups</code>, <code>logs</code>, <code>cache</code>, <code>includes/logs</code>. (Vedi aggiornamento del 27/04/2025)</li>
</ul>
<h3>5. Problemi Modulo Massa Inerziale</h3>
<h4>5.1 Modal Dimensioni Errate o Non Visibile (v2.4.2)</h4>
<pre><code>Sintomo: Modal massa inerziale non raggiunge 1400px o appare con dimensioni sbagliate</code></pre>
<p><strong>Soluzioni</strong>:</p>
<ol>
<li><strong>Cache Browser</strong>: Forzare refresh con Ctrl+F5 o pulire cache</li>
<li><strong>CSS Timestamp</strong>: Verificare che modal.css sia caricato con timestamp</li>
<li><strong>Dimensioni Target</strong>: Modal dovrebbe essere 1400px larghezza massima</li>
<li><strong>Conflitti CSS</strong>: Verificare che non ci siano override da altri CSS</li>
</ol>
<p><strong>Verifica Dimensioni</strong>:</p>
<pre><code class="language-javascript">// Aprire console browser e verificare
const modal = document.querySelector('#inertialMassModal .modal-container');
console.log('Larghezza:', modal.offsetWidth, '(target: 1400px)');
console.log('Altezza:', modal.offsetHeight);
console.log('CSS applicato:', getComputedStyle(modal).maxWidth);</code></pre>
<p><strong>Fix Manuale</strong>:</p>
<pre><code class="language-javascript">// Se il modal non si ingrandisce, forzare via JavaScript
const modal = document.querySelector('#inertialMassModal .modal-container');
modal.style.cssText = `
    width: 98vw !important;
    max-width: 1400px !important;
    height: 95vh !important;
`;</code></pre>
<h4>5.2 Risultati Non a Schermo Intero (v2.4.2)</h4>
<pre><code>Sintomo: Risultati mostrati insieme al form invece che a schermo intero</code></pre>
<p><strong>Soluzioni</strong>:</p>
<ol>
<li><strong>Verifica JavaScript</strong>: Controllare che displayResults() nasconda il form</li>
<li><strong>CSS Risultati</strong>: Verificare posizionamento assoluto della sezione risultati</li>
<li><strong>Console Debug</strong>: Verificare log &quot;Risultati mostrati a SCHERMO INTERO&quot;</li>
</ol>
<p><strong>Verifica Stato</strong>:</p>
<pre><code class="language-javascript">// Verificare visibilità elementi
const form = document.querySelector('.modal-body');
const results = document.getElementById('results-section');
console.log('Form visibile:', form.style.display !== 'none');
console.log('Risultati visibili:', results.style.display !== 'none');
console.log('Risultati posizione:', getComputedStyle(results).position);</code></pre>
<h4>5.3 Raddoppio Icone nei Risultati (RISOLTO v2.1.0)</h4>
<pre><code>Sintomo: Icone duplicate nei titoli delle sezioni (📊📊 Distribuzione Forze, 🤖🤖 Analisi AI)</code></pre>
<p><strong>Causa</strong>: Problema di pulizia HTML nel rendering dinamico dei risultati</p>
<p><strong>Soluzione</strong>:</p>
<ol>
<li><strong>Aggiornamento automatico</strong>: Il problema è stato risolto nella versione 2.1.0</li>
<li><strong>Verifica versione</strong>: Controllare che il file <code>assets/js/modal.js</code> contenga la correzione:
<pre><code class="language-javascript">// Riga ~858 in displayResults()
resultsContent.innerHTML = ''; // Pulizia contenuto esistente
const newHTML = generateResultsHTML(results);
resultsContent.innerHTML = newHTML;</code></pre></li>
</ol>
<h3>6. Problemi Sistema Log</h3>
<h4>6.1 Errore 500 nella Pulizia Log (RISOLTO Giugno 2025)</h4>
<pre><code>Sintomo: Errore 500 Internal Server Error quando si tenta di pulire i log
Errore: Fatal error: Uncaught Error: Class "ZipArchive" not found</code></pre>
<p><strong>Causa</strong>: Estensione PHP ZIP non disponibile in XAMPP</p>
<p><strong>Soluzione</strong>:</p>
<ol>
<li><strong>Fix automatico</strong>: Il problema è stato risolto con controllo compatibilità in <code>admin/clear_logs.php</code></li>
<li><strong>Verifica fix</strong>: Il sistema ora controlla automaticamente la disponibilità di ZipArchive:
<pre><code class="language-php">if (class_exists('ZipArchive')) {
   // Crea backup ZIP
} else {
   // Salva backup come directory
}</code></pre></li>
<li><strong>Abilitazione manuale ZIP</strong> (opzionale):
<ul>
<li>Aprire <code>php.ini</code></li>
<li>Decommentare: <code>extension=zip</code></li>
<li>Riavviare Apache</li>
</ul></li>
</ol>
<h4>6.2 JSON Invalido nei Log (RISOLTO Giugno 2025)</h4>
<pre><code>Sintomo: Risposta non valida dal server durante pulizia log</code></pre>
<p><strong>Causa</strong>: Output HTML mescolato con JSON a causa dell'errore ZipArchive</p>
<p><strong>Soluzione</strong>: Risolto automaticamente con il fix del punto 6.1</p>
<h3>7. Problemi Diagrammi Mermaid</h3>
<h4>7.1 Errore JavaScript Mermaid (RISOLTO Giugno 2025)</h4>
<pre><code>Sintomo: Could not find a suitable point for the given distance
File: docs/relazione_tecnica.html</code></pre>
<p><strong>Causa</strong>: Configurazione problematica e connessioni multiple complesse nel diagramma</p>
<p><strong>Soluzione</strong>:</p>
<ol>
<li>
<p><strong>Configurazione migliorata</strong>:</p>
<pre><code class="language-javascript">mermaid.initialize({
 startOnLoad: false,
 flowchart: {
   curve: 'linear',        // Cambiato da 'basis'
   padding: 20,            // Aumentato da 10
   useMaxWidth: true,
   htmlLabels: false,      // Cambiato da true
   nodeSpacing: 100,       // Aumentato da 50
   rankSpacing: 100,       // Aumentato da 50
   diagramPadding: 20,     // Aggiunto
   defaultRenderer: 'dagre' // Specificato
 }
});</code></pre>
</li>
<li>
<p><strong>Connessioni semplificate</strong>: Trasformate le connessioni multiple (D &amp; E &amp; F --&gt; G) in connessioni singole (D --&gt; G, E --&gt; G, F --&gt; G)</p>
</li>
<li>
<p><strong>Verifica funzionamento</strong>: Il diagramma ora si renderizza correttamente senza errori JavaScript</p>
</li>
<li>
<p><strong>Cache browser</strong>: Se il problema persiste, pulire la cache:</p>
<pre><code class="language-powershell"># Chrome
del /S /Q "%LocalAppData%\Google\Chrome\User Data\Default\Cache\*"

# Firefox
del /S /Q "%AppData%\Mozilla\Firefox\Profiles\*\cache2\*"</code></pre>
</li>
</ol>
<h4>5.2 Errori API Deepseek</h4>
<pre><code>Error: Failed to fetch from LLM service</code></pre>
<p><strong>Soluzioni</strong>:</p>
<ol>
<li>
<p>Verificare chiave API in <code>.env</code>:</p>
<pre><code>DEEPSEEK_API_KEY=***********************************</code></pre>
</li>
<li>
<p>Controllare connessione:</p>
<pre><code class="language-powershell">curl -X POST "https://api.deepseek.com/v1/chat/completions" ^
    -H "Authorization: Bearer YOUR_API_KEY" ^
    -H "Content-Type: application/json"</code></pre>
</li>
<li>
<p>Verificare rate limiting:</p>
<pre><code class="language-sql">SELECT COUNT(*) FROM inertial_mass_api_logs
WHERE user_id = ? AND timestamp &gt; DATE_SUB(NOW(), INTERVAL 1 MINUTE);</code></pre>
</li>
</ol>
<h4>5.3 Scroll Non Funzionante</h4>
<pre><code>Sintomo: Sezione risultati non scrollabile</code></pre>
<p><strong>Soluzioni</strong>:</p>
<ol>
<li>
<p>Verificare CSS in <code>assets/css/modal.css</code>:</p>
<pre><code class="language-css">.modal-results {
   max-height: 70vh;
   overflow-y: auto;
}</code></pre>
</li>
<li>
<p>Controllare JavaScript:</p>
<pre><code class="language-javascript">// Verificare che l'elemento abbia l'ID corretto
const resultsSection = document.getElementById('results-section');</code></pre>
</li>
</ol>
<h4>5.4 Calcolo Interrotto</h4>
<pre><code>Error: Calculation timeout or incomplete</code></pre>
<p><strong>Soluzioni</strong>:</p>
<ol>
<li>
<p>Verificare timeout in <code>llm_service.php</code>:</p>
<pre><code class="language-php">$timeout = 180; // 3 minuti</code></pre>
</li>
<li>
<p>Controllare log API:</p>
<pre><code class="language-sql">SELECT * FROM inertial_mass_api_logs
WHERE status = 'error'
ORDER BY timestamp DESC LIMIT 10;</code></pre>
</li>
<li>
<p>Pulire cache se necessario:</p>
<pre><code class="language-powershell">del /S /Q "inertial_mass\cache\*"</code></pre>
</li>
</ol>
<h4>5.5 Debug Modulo Massa Inerziale</h4>
<pre><code class="language-javascript">// Abilitare debug nel browser
localStorage.setItem('inertial_mass_debug', 'true');

// Verificare stato modulo
console.log('Stato modulo:', inertialMassState);

// Controllare dati form
console.log('Dati form:', collectFormData());</code></pre>
<p><strong>Log specifici</strong>:</p>
<pre><code class="language-powershell"># Log modulo massa inerziale
Get-Content -Path logs\inertial_mass.log -Tail 50

# Log API calls
Get-Content -Path logs\ai.log -Tail 20</code></pre>
<h2>Debug</h2>
<h3>1. Log System</h3>
<h4>Abilitare Debug</h4>
<pre><code class="language-php">// .env
DEBUG=true
LOG_LEVEL=debug

// Esempio uso
Logger::debug('Test message', ['data' =&gt; $value]);</code></pre>
<h4>Visualizzare Log</h4>
<pre><code class="language-powershell"># Ultimi 50 errori
Get-Content -Path logs\error.log -Tail 50

# Log in tempo reale
Get-Content -Path logs\error.log -Wait</code></pre>
<h3>2. Database Debug</h3>
<h4>Query Log</h4>
<pre><code class="language-sql">-- Abilitare log query
SET GLOBAL general_log = 'ON';
SET GLOBAL general_log_file = 'C:/xampp/mysql/data/query.log';

-- Visualizzare query lente
SHOW VARIABLES LIKE '%slow%';</code></pre>
<h4>Analisi Query</h4>
<pre><code class="language-sql">-- Performance schema
SELECT * FROM performance_schema.events_statements_summary_by_digest
ORDER BY sum_timer_wait DESC LIMIT 10;</code></pre>
<h3>3. Frontend Debug</h3>
<h4>Console Browser</h4>
<pre><code class="language-javascript">// Abilitare debug
localStorage.setItem('debug', 'true');

// Log personalizzati
console.debug('Debug info:', data);</code></pre>
<h4>Network Analysis</h4>
<ol>
<li>Chrome DevTools (F12)</li>
<li>Tab Network</li>
<li>Filtrare per:
<ul>
<li>XHR</li>
<li>JS</li>
<li>CSS</li>
</ul></li>
</ol>
<h3>4. API Debug</h3>
<h4>Test Endpoint</h4>
<pre><code class="language-powershell"># GET request
curl -X GET "http://localhost/asdp/api/cadastral_data.php?comune=Roma"

# POST request
curl -X POST "http://localhost/asdp/api/calculate_seismic_params.php" ^
     -H "Content-Type: application/json" ^
     -d "{\"lat\":41.9,\"lon\":12.5}"</code></pre>
<h4>Validazione Token</h4>
<pre><code class="language-php">// Decodifica token
$token = $_SERVER['HTTP_AUTHORIZATION'];
$decoded = JWT::decode($token, $key, array('HS256'));</code></pre>
<h2>Procedure di Recovery</h2>
<h3>1. Database Corrotto</h3>
<pre><code class="language-powershell"># Backup dati
mysqldump -u root -p asdp_db &gt; backup_before_repair.sql

# Riparare tabelle
mysqlcheck -u root -p --auto-repair asdp_db

# Se necessario, ripristinare
mysql -u root -p asdp_db &lt; backup_before_repair.sql</code></pre>
<h3>2. File System Corrotto</h3>
<pre><code class="language-powershell"># Verificare integrità
chkdsk C: /f

# Ripristinare da backup
xcopy /E /I /Y backup\* C:\xampp\htdocs\asdp\</code></pre>
<h3>3. Cache Corrotta</h3>
<pre><code class="language-powershell"># Pulire tutte le cache
del /S /Q temp\cache\*
del /S /Q temp\sessions\*

# Riavviare servizi
net stop Apache2.4
net start Apache2.4</code></pre>
<h2>Note Importanti</h2>
<h3>Prevenzione</h3>
<ol>
<li>Backup regolari</li>
<li>Monitoraggio log</li>
<li>Test periodici</li>
<li>Aggiornamenti sistema</li>
</ol>
<h3>Best Practices</h3>
<ol>
<li>Documentare errori</li>
<li>Mantenere log puliti</li>
<li>Verificare backup</li>
<li>Test di recovery</li>
</ol>
<h1>Misure di Sicurezza</h1>
<p>Ultimo aggiornamento: 20/01/2024</p>
<h2>1. Autenticazione e Autorizzazione</h2>
<h3>1.1 Sistema di Autenticazione</h3>
<ul>
<li>Autenticazione basata su JWT</li>
<li>Token con scadenza configurabile</li>
<li>Refresh token per sessioni lunghe</li>
<li>Blocco account dopo tentativi falliti</li>
</ul>
<h3>1.2 Gestione Permessi</h3>
<ul>
<li>RBAC (Role Based Access Control)</li>
<li>Permessi granulari per funzionalità</li>
<li>Separazione ruoli admin/user</li>
<li>Audit log delle operazioni</li>
</ul>
<h2>2. Protezione Dati</h2>
<h3>2.1 Crittografia</h3>
<ul>
<li>Dati sensibili crittografati a riposo</li>
<li>Comunicazioni via HTTPS</li>
<li>Hashing password con algoritmi sicuri</li>
<li>Chiavi di crittografia gestite in modo sicuro</li>
</ul>
<h3>2.2 Backup</h3>
<ul>
<li>Backup automatici giornalieri</li>
<li>Crittografia dei backup</li>
<li>Retention policy configurabile</li>
<li>Test di restore periodici</li>
</ul>
<h2>3. Sicurezza Applicativa</h2>
<h3>3.1 Protezione Input</h3>
<ul>
<li>Validazione input lato server</li>
<li>Sanitizzazione dati</li>
<li>Protezione XSS</li>
<li>Prevenzione SQL Injection</li>
</ul>
<h3>3.2 Rate Limiting</h3>
<ul>
<li>Limite richieste per IP</li>
<li>Limite richieste per utente</li>
<li>Protezione DDoS</li>
<li>Blacklist IP malevoli</li>
</ul>
<h2>4. Monitoraggio</h2>
<h3>4.1 Logging</h3>
<ul>
<li>Log accessi</li>
<li>Log operazioni critiche</li>
<li>Log errori di sistema</li>
<li>Rotazione log automatica</li>
</ul>
<h3>4.2 Alerting</h3>
<ul>
<li>Notifiche tentativi non autorizzati</li>
<li>Alert anomalie di sistema</li>
<li>Monitoraggio risorse</li>
<li>Threshold configurabili</li>
</ul>
<h2>5. Compliance</h2>
<h3>5.1 GDPR</h3>
<ul>
<li>Consenso utente</li>
<li>Diritto all'oblio</li>
<li>Esportazione dati</li>
<li>Privacy by design</li>
</ul>
<h3>5.2 Sicurezza Dati</h3>
<ul>
<li>Minimizzazione dati</li>
<li>Retention policy</li>
<li>Cifratura end-to-end</li>
<li>Accesso controllato</li>
</ul>
<h2>6. Hardening Sistema</h2>
<h3>6.1 Server</h3>
<ul>
<li>Firewall configurato</li>
<li>Porte non necessarie chiuse</li>
<li>Servizi non essenziali disabilitati</li>
<li>Updates automatici</li>
</ul>
<h3>6.2 Database</h3>
<ul>
<li>Accesso solo da localhost</li>
<li>Utenti con privilegi minimi</li>
<li>Backup automatizzati</li>
<li>Query parametrizzate</li>
</ul>
<h2>7. Best Practices</h2>
<h3>7.1 Sviluppo Sicuro</h3>
<ul>
<li>Code review obbligatorie</li>
<li>Test di sicurezza automatizzati</li>
<li>Dependency scanning</li>
<li>Security headers HTTP</li>
</ul>
<h3>7.2 Operazioni</h3>
<ul>
<li>Procedure documentate</li>
<li>Training personale</li>
<li>Incident response plan</li>
<li>Disaster recovery</li>
</ul>
<h2>8. Misure Aggiuntive</h2>
<h3>8.1 Frontend</h3>
<ul>
<li>CSP (Content Security Policy)</li>
<li>Anti-CSRF tokens</li>
<li>Secure cookies</li>
<li>Frame protection</li>
</ul>
<h3>8.2 API</h3>
<ul>
<li>Autenticazione JWT</li>
<li>Rate limiting</li>
<li>Input validation</li>
<li>Error handling sicuro</li>
</ul>
<h2>9. Audit e Compliance</h2>
<h3>9.1 Audit Periodici</h3>
<ul>
<li>Scan vulnerabilità</li>
<li>Penetration testing</li>
<li>Review configurazioni</li>
<li>Analisi log</li>
</ul>
<h3>9.2 Documentazione</h3>
<ul>
<li>Politiche sicurezza</li>
<li>Procedure operative</li>
<li>Incident response</li>
<li>Disaster recovery</li>
</ul>
<h2>Note Importanti</h2>
<ol>
<li>Aggiornare regolarmente le password</li>
<li>Monitorare gli accessi sospetti</li>
<li>Mantenere software aggiornato</li>
<li>Backup regolari e verificati </li>
</ol>
<h1>Performance e Ottimizzazioni</h1>
<h2>Ottimizzazioni JavaScript</h2>
<h3>Sistema di Minificazione</h3>
<ul>
<li>Implementato sistema di minificazione JavaScript usando JShrink</li>
<li>Localizzazione: <code>tools/minify_js.php</code></li>
<li>Riduzione media dimensione file: 45%</li>
</ul>
<h3>File Minificati</h3>
<table>
<thead>
<tr>
<th>File Originale</th>
<th>File Minificato</th>
<th>Riduzione</th>
</tr>
</thead>
<tbody>
<tr>
<td>map.js</td>
<td>map.min.js</td>
<td>44.08%</td>
</tr>
<tr>
<td>ui.js</td>
<td>ui.min.js</td>
<td>47.76%</td>
</tr>
<tr>
<td>search.js</td>
<td>search.min.js</td>
<td>~45%</td>
</tr>
<tr>
<td>helpers.js</td>
<td>helpers.min.js</td>
<td>~45%</td>
</tr>
<tr>
<td>account.js</td>
<td>account.min.js</td>
<td>~45%</td>
</tr>
</tbody>
</table>
<h3>Gestione Popup</h3>
<ul>
<li>Ottimizzato il caricamento asincrono dei popup</li>
<li>Integrata la logica account nel gestore popup principale</li>
<li>Rimosso caricamento separato di account.min.js</li>
<li>Implementata reinizializzazione intelligente dei form</li>
</ul>
<h2>Ottimizzazioni UI/UX</h2>
<h3>Favicon</h3>
<ul>
<li>Aggiunto favicon personalizzato ASDP</li>
<li>Formato: ICO multi-risoluzione (16x16, 32x32)</li>
<li>Posizione: <code>/favicon.ico</code></li>
</ul>
<h3>Pulizia Codice</h3>
<ul>
<li>Rimosso sistema di cambio tema non utilizzato</li>
<li>Eliminati file non necessari:
<ul>
<li>theme.js</li>
<li>theme.css</li>
</ul></li>
<li>Ottimizzata struttura HTML in register.php</li>
</ul>
<h2>Best Practices</h2>
<h3>Caricamento Script</h3>
<ul>
<li>Utilizzo attributo <code>defer</code> per script non critici</li>
<li>Caricamento asincrono delle API esterne</li>
<li>Gestione intelligente delle dipendenze</li>
</ul>
<h3>Gestione Cache</h3>
<ul>
<li>Implementazione cache per i popup</li>
<li>Riutilizzo componenti già caricati</li>
<li>Gestione efficiente stato form</li>
</ul>
<h2>Monitoraggio Performance</h2>
<h3>Metriche da Monitorare</h3>
<ul>
<li>Tempo di caricamento pagina</li>
<li>Dimensione file JavaScript</li>
<li>Tempo di risposta server</li>
<li>Utilizzo memoria</li>
</ul>
<h3>Strumenti di Analisi</h3>
<ul>
<li>Chrome DevTools</li>
<li>PageSpeed Insights</li>
<li>WebPageTest</li>
<li>GTmetrix</li>
</ul>
<h2>Prossime Ottimizzazioni</h2>
<h3>Front-end</h3>
<ol>
<li>Ottimizzazione immagini</li>
<li>Compressione GZIP</li>
<li>Cache headers</li>
<li>Ottimizzazione font</li>
<li>Lazy loading immagini</li>
</ol>
<h3>Back-end</h3>
<ol>
<li>Ottimizzazione query database</li>
<li>Caching risultati</li>
<li>Compressione risposta server</li>
</ol>
<h2>Linee Guida Manutenzione</h2>
<ol>
<li>Testare performance dopo ogni modifica</li>
<li>Mantenere file minificati aggiornati</li>
<li>Monitorare dimensione bundle</li>
<li>Verificare compatibilità browser</li>
<li>Testare su dispositivi mobili</li>
</ol>
<h2>Note Tecniche</h2>
<ul>
<li>Utilizzare gli strumenti di minificazione forniti</li>
<li>Seguire le best practices di caricamento</li>
<li>Mantenere documentazione aggiornata</li>
<li>Effettuare backup prima delle ottimizzazioni </li>
</ul>
<h2>Dashboard Amministrativa</h2>
<h3>Ottimizzazioni Pianificate</h3>
<ol>
<li>
<p><strong>Caching Statistiche</strong></p>
<ul>
<li>Implementazione cache Redis per statistiche frequenti</li>
<li>Aggiornamento cache ogni 5 minuti</li>
<li>Cache selettiva per dati critici</li>
</ul>
</li>
<li>
<p><strong>Query Database</strong></p>
<ul>
<li>Ottimizzazione query statistiche</li>
<li>Indici per tabelle frequentemente accedute</li>
<li>Query asincrone per dati non critici</li>
</ul>
</li>
<li>
<p><strong>Caricamento Dati</strong></p>
<ul>
<li>Implementazione lazy loading</li>
<li>Caricamento asincrono widget</li>
<li>Aggiornamento selettivo componenti</li>
</ul>
</li>
<li>
<p><strong>Compressione</strong></p>
<ul>
<li>Compressione GZIP per response HTTP</li>
<li>Minificazione assets (JS/CSS)</li>
<li>Ottimizzazione immagini</li>
</ul>
</li>
</ol>
<h3>Metriche di Monitoraggio</h3>
<ul>
<li>Tempo di risposta API</li>
<li>Utilizzo memoria</li>
<li>Carico CPU</li>
<li>Latenza database </li>
</ul>
<h1>Metodo di Calcolo Sismico</h1>
<p>Ultimo aggiornamento: 20/01/2024</p>
<h2>1. Introduzione</h2>
<p>Il calcolo sismico viene effettuato secondo le NTC 2018 (Norme Tecniche per le Costruzioni) e relative circolari applicative. Il processo si articola in diverse fasi che vengono eseguite in sequenza.</p>
<h2>2. Parametri di Input</h2>
<h3>2.1 Parametri Geografici</h3>
<ul>
<li>Latitudine (LAT)</li>
<li>Longitudine (LNG)</li>
<li>Categoria di sottosuolo</li>
<li>Categoria topografica</li>
</ul>
<h3>2.2 Parametri Strutturali</h3>
<ul>
<li>Vita nominale di progetto (VN)</li>
<li>Classe d'uso (Cu)</li>
<li>Periodo di riferimento (VR = VN × Cu)</li>
<li>Fattore di struttura (q)</li>
<li>Coefficiente di smorzamento (ξ)</li>
</ul>
<h2>3. Fasi di Calcolo</h2>
<h3>3.1 Determinazione Parametri di Base</h3>
<ol>
<li>Recupero ag, F0, TC* dai dati di riferimento</li>
<li>Interpolazione valori per il punto specifico</li>
<li>Calcolo periodo di ritorno TR</li>
</ol>
<h3>3.2 Coefficienti di Amplificazione</h3>
<ol>
<li>Coefficiente stratigrafico SS</li>
<li>Coefficiente topografico ST</li>
<li>Coefficiente S = SS × ST</li>
<li>Coefficiente CC per categoria sottosuolo</li>
</ol>
<h3>3.3 Periodi Caratteristici</h3>
<ol>
<li>TC = CC × TC*</li>
<li>TB = TC / 3</li>
<li>TD = 4.0 × (ag/g) + 1.6</li>
</ol>
<h2>4. Spettri di Risposta</h2>
<h3>4.1 Spettro Elastico Orizzontale</h3>
<p>Per 0 ≤ T &lt; TB:</p>
<pre><code>Se(T) = ag × S × η × F0 × [T/(TB) + 1/(η×F0) × (1 - T/TB)]</code></pre>
<p>Per TB ≤ T &lt; TC:</p>
<pre><code>Se(T) = ag × S × η × F0</code></pre>
<p>Per TC ≤ T &lt; TD:</p>
<pre><code>Se(T) = ag × S × η × F0 × (TC/T)</code></pre>
<p>Per TD ≤ T:</p>
<pre><code>Se(T) = ag × S × η × F0 × (TC×TD)/(T²)</code></pre>
<h3>4.2 Spettro di Progetto</h3>
<p>Ottenuto riducendo le ordinate dello spettro elastico mediante il fattore q:</p>
<pre><code>Sd(T) = Se(T) / q</code></pre>
<h2>5. Validazione Risultati</h2>
<h3>5.1 Controlli Automatici</h3>
<ol>
<li>Verifica range parametri di input</li>
<li>Controllo coerenza risultati</li>
<li>Validazione spettri generati</li>
<li>Verifica limiti normativi</li>
</ol>
<h3>5.2 Verifiche Manuali</h3>
<ol>
<li>Confronto con casi noti</li>
<li>Verifica andamento spettri</li>
<li>Controllo valori caratteristici</li>
<li>Validazione coefficienti</li>
</ol>
<h2>6. Output Generati</h2>
<h3>6.1 Parametri Calcolati</h3>
<ul>
<li>ag: accelerazione orizzontale massima</li>
<li>F0: fattore amplificazione spettrale</li>
<li>TC*: periodo inizio tratto velocità costante</li>
<li>SS: coefficiente amplificazione stratigrafica</li>
<li>ST: coefficiente amplificazione topografica</li>
<li>S: coefficiente che tiene conto categoria sottosuolo</li>
</ul>
<h3>6.2 Spettri</h3>
<ul>
<li>Spettro elastico orizzontale</li>
<li>Spettro elastico verticale</li>
<li>Spettro di progetto SLV</li>
<li>Spettro di progetto SLD</li>
<li>Spettro di progetto SLO</li>
</ul>
<h2>7. Ottimizzazioni</h2>
<h3>7.1 Performance</h3>
<ol>
<li>Caching risultati frequenti</li>
<li>Ottimizzazione calcoli</li>
<li>Parallelizzazione processi</li>
<li>Gestione memoria</li>
</ol>
<h3>7.2 Precisione</h3>
<ol>
<li>Interpolazione dati precisa</li>
<li>Arrotondamenti controllati</li>
<li>Validazione step-by-step</li>
<li>Gestione casi limite</li>
</ol>
<h2>Note Tecniche</h2>
<ol>
<li>Tutti i calcoli vengono eseguiti in doppia precisione</li>
<li>I risultati vengono arrotondati solo nella presentazione finale</li>
<li>Le interpolazioni utilizzano il metodo bilineare</li>
<li>I grafici vengono generati con precisione 0.01s</li>
</ol>
<h2>Riferimenti Normativi</h2>
<ul>
<li>NTC 2018 (D.M. 17/01/2018)</li>
<li>Circolare applicativa n.7 del 21/01/2019</li>
<li>Eurocodice 8 (EN 1998-1)</li>
</ul>
<h2>Stati Limite</h2>
<h3>SLO (Stato Limite di Operatività)</h3>
<ul>
<li><strong>Probabilità</strong>: 81% in VR</li>
<li><strong>Periodo</strong>: TR = 30 anni</li>
<li><strong>Descrizione</strong>: Dopo il terremoto, la costruzione nel suo complesso non deve subire danni ed interruzioni d'uso significative.</li>
</ul>
<h3>SLD (Stato Limite di Danno)</h3>
<ul>
<li><strong>Probabilità</strong>: 63% in VR</li>
<li><strong>Periodo</strong>: TR = 50 anni</li>
<li><strong>Descrizione</strong>: Dopo il terremoto, la costruzione nel suo complesso subisce danni tali da non mettere a rischio gli utenti.</li>
</ul>
<h3>SLV (Stato Limite di salvaguardia della Vita)</h3>
<ul>
<li><strong>Probabilità</strong>: 10% in VR</li>
<li><strong>Periodo</strong>: TR = 475 anni</li>
<li><strong>Descrizione</strong>: Dopo il terremoto, la costruzione subisce rotture e crolli dei componenti non strutturali ed impiantistici.</li>
</ul>
<h3>SLC (Stato Limite di prevenzione del Collasso)</h3>
<ul>
<li><strong>Probabilità</strong>: 5% in VR</li>
<li><strong>Periodo</strong>: TR = 975 anni</li>
<li><strong>Descrizione</strong>: Dopo il terremoto, la costruzione subisce gravi rotture e crolli dei componenti non strutturali ed impiantistici.</li>
</ul>
<h2>Parametri Input</h2>
<h3>1. Vita Nominale (VN)</h3>
<pre><code class="language-php">$vita_nominale = [
    'opere_provvisorie' =&gt; 10,  // Opere provvisorie, provvisionali, strutture in fase costruttiva
    'opere_ordinarie' =&gt; 50,    // Opere ordinarie, ponti, infrastrutture
    'opere_grandi' =&gt; 100       // Grandi opere, ponti, infrastrutture di grandi dimensioni
];</code></pre>
<h3>2. Classe d'Uso (CU)</h3>
<pre><code class="language-php">$classe_uso = [
    'I' =&gt; 0.7,    // Costruzioni con presenza solo occasionale di persone
    'II' =&gt; 1.0,   // Costruzioni con normali affollamenti
    'III' =&gt; 1.5,  // Costruzioni con affollamenti significativi
    'IV' =&gt; 2.0    // Costruzioni con funzioni pubbliche o strategiche importanti
];</code></pre>
<h3>3. Categoria Sottosuolo</h3>
<pre><code class="language-php">$categoria_sottosuolo = [
    'A' =&gt; ['descrizione' =&gt; 'Ammassi rocciosi affioranti o terreni molto rigidi',
            'vs30' =&gt; '&gt; 800 m/s'],
    'B' =&gt; ['descrizione' =&gt; 'Rocce tenere e depositi di terreni a grana grossa molto addensati',
            'vs30' =&gt; '360-800 m/s'],
    'C' =&gt; ['descrizione' =&gt; 'Depositi di terreni a grana grossa mediamente addensati',
            'vs30' =&gt; '180-360 m/s'],
    'D' =&gt; ['descrizione' =&gt; 'Depositi di terreni a grana grossa scarsamente addensati',
            'vs30' =&gt; '&lt; 180 m/s'],
    'E' =&gt; ['descrizione' =&gt; 'Terreni dei sottosuoli di tipo C o D per spessore non superiore a 20 m']
];</code></pre>
<h3>4. Categoria Topografica</h3>
<pre><code class="language-php">$categoria_topografica = [
    'T1' =&gt; ['descrizione' =&gt; 'Superficie pianeggiante, pendii e rilievi isolati con inclinazione media i ≤ 15°',
             'st' =&gt; 1.0],
    'T2' =&gt; ['descrizione' =&gt; 'Pendii con inclinazione media i &gt; 15°',
             'st' =&gt; 1.2],
    'T3' =&gt; ['descrizione' =&gt; 'Rilievi con larghezza in cresta molto minore che alla base e inclinazione media 15° ≤ i ≤ 30°',
             'st' =&gt; 1.2],
    'T4' =&gt; ['descrizione' =&gt; 'Rilievi con larghezza in cresta molto minore che alla base e inclinazione media i &gt; 30°',
             'st' =&gt; 1.4]
];</code></pre>
<h2>Formule</h2>
<h3>1. Periodo di Riferimento (VR)</h3>
<pre><code class="language-php">function calcolaPeriodoRiferimento($vn, $cu) {
    return $vn * $cu;
}</code></pre>
<h3>2. Tempo di Ritorno (TR)</h3>
<pre><code class="language-php">function calcolaTempoRitorno($vr, $pvr) {
    return -$vr / log(1 - $pvr);
}</code></pre>
<h3>3. Accelerazione al Suolo (ag)</h3>
<pre><code class="language-php">function calcolaAccelerazione($lat, $lon, $tr) {
    // Interpolazione dai dati di pericolosità sismica
    $ag = interpolazioneGriglia($lat, $lon, $tr);
    return $ag;
}</code></pre>
<h3>4. Coefficienti di Amplificazione</h3>
<pre><code class="language-php">function calcolaCoefficienti($categoria_suolo, $categoria_topografica) {
    // Coefficiente stratigrafico
    $ss = calcolaSs($categoria_suolo);

    // Coefficiente topografico
    $st = $categoria_topografica['st'];

    // Coefficiente di amplificazione
    $s = $ss * $st;

    return $s;
}</code></pre>
<h3>5. Spettro di Risposta Elastico</h3>
<pre><code class="language-php">function calcolaSpettroRisposta($ag, $f0, $tc, $s) {
    $spettro = [];

    // Periodo T
    for ($t = 0; $t &lt;= 4; $t += 0.01) {
        if ($t &lt;= $tc) {
            $se = $ag * $s * $f0;
        } else {
            $se = $ag * $s * $f0 * ($tc / $t);
        }
        $spettro[] = ['T' =&gt; $t, 'Se' =&gt; $se];
    }

    return $spettro;
}</code></pre>
<h2>Esempio di Calcolo</h2>
<h3>Input</h3>
<pre><code class="language-php">$input = [
    'lat' =&gt; 41.902782,
    'lon' =&gt; 12.496366,
    'vn' =&gt; 50,
    'cu' =&gt; 1.0,
    'categoria_suolo' =&gt; 'B',
    'categoria_topografica' =&gt; 'T1'
];</code></pre>
<h3>Calcolo</h3>
<pre><code class="language-php">// 1. Periodo di riferimento
$vr = calcolaPeriodoRiferimento($input['vn'], $input['cu']);

// 2. Stati limite
$stati_limite = [
    'SLO' =&gt; ['pvr' =&gt; 0.81, 'tr' =&gt; null],
    'SLD' =&gt; ['pvr' =&gt; 0.63, 'tr' =&gt; null],
    'SLV' =&gt; ['pvr' =&gt; 0.10, 'tr' =&gt; null],
    'SLC' =&gt; ['pvr' =&gt; 0.05, 'tr' =&gt; null]
];

foreach ($stati_limite as $stato =&gt; &amp;$parametri) {
    // Calcolo TR
    $parametri['tr'] = calcolaTempoRitorno($vr, $parametri['pvr']);

    // Parametri spettrali
    $ag = calcolaAccelerazione($input['lat'], $input['lon'], $parametri['tr']);
    $f0 = calcolaF0($input['lat'], $input['lon'], $parametri['tr']);
    $tc = calcolaTC($input['lat'], $input['lon'], $parametri['tr']);

    // Coefficienti di amplificazione
    $s = calcolaCoefficienti($input['categoria_suolo'], $input['categoria_topografica']);

    // Spettro di risposta
    $parametri['spettro'] = calcolaSpettroRisposta($ag, $f0, $tc, $s);
}</code></pre>
<h3>Output</h3>
<pre><code class="language-php">$output = [
    'SLO' =&gt; [
        'TR' =&gt; 30,
        'ag' =&gt; 0.042,
        'F0' =&gt; 2.547,
        'TC' =&gt; 0.279,
        'spettro' =&gt; [...]
    ],
    'SLD' =&gt; [
        'TR' =&gt; 50,
        'ag' =&gt; 0.054,
        'F0' =&gt; 2.562,
        'TC' =&gt; 0.298,
        'spettro' =&gt; [...]
    ],
    'SLV' =&gt; [
        'TR' =&gt; 475,
        'ag' =&gt; 0.123,
        'F0' =&gt; 2.641,
        'TC' =&gt; 0.334,
        'spettro' =&gt; [...]
    ],
    'SLC' =&gt; [
        'TR' =&gt; 975,
        'ag' =&gt; 0.156,
        'F0' =&gt; 2.678,
        'TC' =&gt; 0.345,
        'spettro' =&gt; [...]
    ]
];</code></pre>
<h2>Nuove Funzionalità (06/01/2024)</h2>
<h3>Ottimizzazioni Calcolo</h3>
<ol>
<li>Miglioramento precisione interpolazione dati</li>
<li>Nuovi controlli di validazione input</li>
<li>Gestione ottimizzata della cache</li>
<li>Logging dettagliato delle operazioni</li>
<li>Gestione errori avanzata</li>
</ol>
<h3>Validazione Risultati</h3>
<ol>
<li>Controlli automatici di coerenza</li>
<li>Confronto con valori attesi</li>
<li>Verifica limiti normativi</li>
<li>Log dettagliato delle anomalie</li>
<li>Sistema di notifica errori</li>
</ol>
<h3>Integrazione API</h3>
<ol>
<li>Nuovo endpoint <code>/api/calculate_seismic_params.php</code></li>
<li>Gestione asincrona dei calcoli</li>
<li>Cache dei risultati frequenti</li>
<li>Validazione input/output</li>
<li>Rate limiting per ottimizzazione</li>
</ol>
<h3>Debug e Testing</h3>
<ol>
<li>Nuovi strumenti di debug in <code>/tools/debug_seismic.php</code></li>
<li>Log dettagliati in <code>/logs/seismic_calc.log</code></li>
<li>Suite di test automatizzati</li>
<li>Strumenti di analisi performance</li>
<li>Sistema di reporting errori</li>
</ol>
<h2>Note Importanti</h2>
<h3>Validazione Input</h3>
<ol>
<li>Coordinate geografiche valide</li>
<li>Parametri vita nominale e classe d'uso corretti</li>
<li>Categorie suolo e topografica ammissibili</li>
<li>Dati di pericolosità sismica disponibili</li>
</ol>
<h3>Precisione Calcoli</h3>
<ol>
<li>Interpolazione dati griglia</li>
<li>Arrotondamento risultati</li>
<li>Gestione errori numerici</li>
<li>Validazione output</li>
</ol>
<h3>Riferimenti Normativi</h3>
<ol>
<li>NTC 2018</li>
<li>Circolare 2019</li>
<li>Eurocodice 8</li>
<li>Ordinanze PCM </li>
</ol>
<h2>Ricalcolo Parametri</h2>
<h3>Interfaccia Utente</h3>
<p>L'applicazione permette il ricalcolo dei parametri sismici attraverso:</p>
<ol>
<li>Input vita nominale (default: 50 anni)</li>
<li>Selezione classe edificio (I, II, III, IV)</li>
<li>Selezione categoria terreno (A, B, C, D, E)</li>
<li>Selezione categoria topografica (T1, T2, T3, T4)</li>
</ol>
<h3>Processo di Ricalcolo</h3>
<pre><code class="language-javascript">async function recalculateSeismicParams() {
    // Raccolta parametri
    const params = {
        lat: currentLocation.lat(),
        lng: currentLocation.lng(),
        nominalLife: parseInt(nominalLife),
        buildingClass: buildingClass,
        soilCategory: soilCategory,
        topographicCategory: topographicCategory
    };

    // Invio al server
    const response = await fetch('api/calculate_seismic_params.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(params)
    });

    // Elaborazione risultati
    const data = await response.json();
    if (data.success) {
        updateSeismicResults(data.data);
    }
}</code></pre>
<h3>Visualizzazione Risultati</h3>
<p>I risultati vengono mostrati in una tabella con:</p>
<ul>
<li>Stati Limite (SLO, SLD, SLV, SLC)</li>
<li>Tempo di ritorno TR [anni]</li>
<li>Accelerazione ag [g]</li>
<li>Fattore amplificazione F0</li>
<li>Periodo TC* [s] </li>
</ul>
<h1>11. Miglioramenti e Aggiornamenti ASDP</h1>
<h2>📋 Aggiornamenti Implementati (Cronologico - Dal più recente)</h2>
<h3>🧪 <strong>✅ COMPLETATO: Consolidamento File di Test Modulo Massa Inerziale</strong> - 15/06/2025</h3>
<p><strong>Ottimizzazione test:</strong> Consolidati file di test per eliminare duplicazioni e migliorare efficacia</p>
<ul>
<li><strong>Analisi completa:</strong> Valutati 2 file di test (1009 righe totali) per utilità e necessità</li>
<li><strong>Consolidamento:</strong> Rimosso <code>test_report_generation.html</code> (408 righe) con funzionalità duplicate</li>
<li><strong>Mantenimento:</strong> Conservato <code>test_real_data_flow.html</code> (601 righe) più completo e significativo</li>
<li><strong>Benefici ottenuti:</strong> -408 righe, manutenzione semplificata, focus su test dati reali</li>
<li><strong>Criteri applicati:</strong> Copertura superiore, test reali vs simulati, valore futuro</li>
<li><strong>Documentazione:</strong> Creato <code>analisi_file_test_massa_inerziale.md</code> con valutazione dettagliata</li>
</ul>
<h3>🧹 <strong>✅ COMPLETATO: Pulizia File Obsoleti e Ottimizzazione Progetto</strong> - 15/06/2025</h3>
<p><strong>Pulizia sistematica:</strong> Rimossi 13 file obsoleti per ottimizzare struttura progetto</p>
<ul>
<li><strong>Analisi completa:</strong> Identificati file di test, debug e log temporanei non più necessari</li>
<li><strong>Rimozione sicura:</strong> 13 file eliminati (test dissipatori, backup test, log debug)</li>
<li><strong>Benefici ottenuti:</strong> -500KB dimensioni, struttura più pulita, manutenzione semplificata</li>
<li><strong>File mantenuti:</strong> Solo utility di produzione essenziali in cartella tools</li>
<li><strong>Documentazione:</strong> Creato <code>file_obsoleti_analisi.md</code> con analisi dettagliata</li>
<li><strong>Aggiornamenti:</strong> <code>app_map.md</code> aggiornato per riflettere nuova struttura</li>
</ul>
<h3>🔍 <strong>✅ VERIFICATO: Utilizzo Dati Reali nel Sistema Report</strong> - 15/06/2025</h3>
<p><strong>Verifica completata:</strong> Confermato che il modulo massa inerziale v2.5.0 utilizza esclusivamente dati reali</p>
<ul>
<li><strong>Obiettivo:</strong> Verificare assenza di dati simulati o hardcoded nel report finale</li>
<li><strong>Risultato:</strong> ✅ Sistema conforme - tutti i dati provengono da fonti reali</li>
<li><strong>Flusso verificato:</strong> Interfaccia ASDP → Raccolta dati → Memorizzazione → Calcolo AI → Report</li>
<li><strong>Correzioni applicate:</strong> Mapping dati corretti in <code>populateReportTemplate()</code></li>
<li><strong>Test creati:</strong> <code>test_real_data_flow.html</code> per verifica interattiva completa</li>
<li><strong>Documentazione:</strong> <code>verifica_dati_reali_v2.5.0.md</code> con analisi dettagliata</li>
</ul>
<h3>📄 <strong>✅ IMPLEMENTATO: Sistema Report Professionale Completo</strong> - 15/06/2025</h3>
<p><strong>Nuova funzionalità:</strong> Generazione automatica report professionale per analisi massa inerziale + dissipatori sismici</p>
<ul>
<li><strong>Versione aggiornata:</strong> Modulo massa inerziale v2.5.0 COMPLETO</li>
<li><strong>Funzionalità principale:</strong> Pulsante &quot;Salva Risultati&quot; genera report stampabile in nuova finestra</li>
<li><strong>Template professionale:</strong> Design coerente con documentazione ASDP, ottimizzato per stampa A4</li>
<li><strong>Contenuto completo:</strong> Dati input, risultati calcolo, raccomandazioni dissipatori, analisi AI</li>
<li><strong>Funzionalità interattive:</strong> Pulsanti stampa e chiusura, responsive design, timestamp</li>
<li><strong>File implementati:</strong>
<ul>
<li><code>inertial_mass/report_template.html</code>: Template HTML professionale (NUOVO)</li>
<li><code>inertial_mass/assets/js/modal.js</code>: Funzioni generazione e popolamento report</li>
<li><code>inertial_mass/test_report_generation.html</code>: Test completo funzionalità (NUOVO)</li>
</ul></li>
<li><strong>Integrazione seamless:</strong> Memorizzazione automatica dati input e risultati per report</li>
<li><strong>Gestione errori:</strong> Fallback e messaggi informativi per problemi di generazione</li>
<li><strong>Compatibilità:</strong> Finestra 1200x800px, stampa A4, tutti i browser moderni</li>
<li><strong>Risultato:</strong> Report professionale completo per documentazione progetti sismici</li>
</ul>
<h3>🔧 <strong>✅ IMPLEMENTATO: Raccomandazioni Dissipatori Sismici</strong> - 15/06/2025</h3>
<p><strong>Nuova funzionalità:</strong> Sistema intelligente per raccomandazioni dissipatori sismici nel modulo massa inerziale</p>
<ul>
<li><strong>Versione aggiornata:</strong> Modulo massa inerziale v2.5.0</li>
<li><strong>Tipologie dissipatori:</strong> 4 categorie (A: 500kN, B: 1000kN, C: 1500kN, D: 2000kN)</li>
<li><strong>Algoritmo ottimizzazione:</strong> Minimizzazione numero dissipatori con efficienza massima</li>
<li><strong>Integrazione UI:</strong> Sezione dedicata nei risultati con styling professionale</li>
<li><strong>Fattori correttivi:</strong> Tipologia strutturale, intensità sismica, obiettivo riduzione 30%</li>
<li><strong>File modificati:</strong>
<ul>
<li><code>inertial_mass/api/local_calculator.php</code>: Funzioni calcolo dissipatori</li>
<li><code>inertial_mass/assets/js/modal.js</code>: Generazione HTML e animazioni</li>
<li><code>inertial_mass/assets/css/damper_styles.css</code>: Stili specifici (NUOVO)</li>
<li><code>js/inertial_mass_integration.js</code>: Caricamento CSS aggiuntivo</li>
</ul></li>
<li><strong>Documentazione aggiornata:</strong> <code>docs/14_massa_inerziale.md</code>, <code>docs/app_map.md</code></li>
<li><strong>Compatibilità:</strong> Modal 1400px, responsive design, animazioni fluide</li>
<li><strong>Risultato:</strong> Raccomandazioni tecniche personalizzate per messa in sicurezza sismica</li>
</ul>
<h3>📦 <strong>✅ PROBLEMA RISOLTO: Struttura Backup ZIP</strong> - 15/06/2025</h3>
<p><strong>Problema risolto:</strong> Il file ZIP di backup ora ha la struttura corretta senza cartelle annidate</p>
<ul>
<li><strong>Struttura corretta ottenuta:</strong> <code>backup.zip\database\files\backup_info.txt</code></li>
<li><strong>Problema precedente:</strong> <code>backup.zip\temp_2025-06-15_11-58-11\database\files\</code></li>
</ul>
<p><strong>Soluzione implementata:</strong></p>
<ul>
<li>Modificata la funzione <code>createZipWithZipArchive()</code> per calcolare correttamente i percorsi relativi</li>
<li>Aggiunta logica per rimuovere il nome della directory temporanea dal percorso ZIP</li>
<li>Normalizzazione dei separatori di percorso per compatibilità ZIP</li>
<li>Aggiornate anche le funzioni <code>createZipWithPowerShell()</code> e <code>createZipWith7Zip()</code> per coerenza</li>
</ul>
<p><strong>File modificati:</strong></p>
<ul>
<li><code>api/backup_process_zip.php</code> - Correzione logica percorsi ZIP</li>
<li><code>tools/test_backup_structure.php</code> - Nuovo file di test per verificare struttura</li>
</ul>
<p><strong>Stato:</strong> 🟢 <strong>RISOLTO</strong></p>
<ul>
<li><strong>Impatto:</strong> Backup ora produce ZIP con struttura pulita e corretta</li>
<li><strong>Test:</strong> Implementato sistema di verifica automatica struttura ZIP</li>
<li><strong>Compatibilità:</strong> Funziona con tutti i metodi di backup (ZipArchive, PowerShell, 7-Zip)</li>
</ul>
<h3>🔧 v2.5.0 (15/01/2025) - Risoluzione Problema Visibilità Modal Massa Inerziale</h3>
<p><strong>Problema identificato</strong>: Il modal del calcolo massa inerziale sismica appariva con tutti i campi invisibili a causa di conflitti CSS con i file principali del progetto.</p>
<p><strong>Causa principale</strong>:</p>
<ul>
<li>Conflitti di specificità CSS tra <code>modal.css</code> e i CSS principali (<code>home.css</code>, <code>compact-info.css</code>)</li>
<li>Stili del progetto principale che sovrascrivevano gli stili del modal</li>
<li>Z-index insufficiente e problemi di layering</li>
</ul>
<p><strong>Soluzioni implementate</strong>:</p>
<h4>1. <strong>Miglioramento CSS con Specificità Massima</strong></h4>
<ul>
<li>Aggiornato <code>inertial_mass/assets/css/modal.css</code> con selettori ad alta specificità</li>
<li>Tutti i selettori ora usano <code>#inertialMassModal</code> come prefisso</li>
<li>Aggiunto <code>!important</code> su tutte le proprietà critiche</li>
<li>Z-index aumentato a 99999 per garantire il layering corretto</li>
</ul>
<h4>2. <strong>Regole CSS di Emergenza</strong></h4>
<ul>
<li>Aggiunta sezione di regole CSS che forzano la visibilità di tutti gli elementi</li>
<li>Protezione contro <code>display: none</code>, <code>visibility: hidden</code>, <code>opacity: 0</code></li>
<li>Stili inline forzati per elementi critici</li>
</ul>
<h4>3. <strong>Miglioramenti JavaScript</strong></h4>
<ul>
<li>Aggiornato <code>inertial_mass/assets/js/modal.js</code> per applicare stili inline forzati</li>
<li>Implementazione di visibilità forzata per tutti gli elementi del form</li>
<li>Debug automatico della visibilità degli elementi</li>
</ul>
<h4>4. <strong>Sistema di Debug</strong></h4>
<ul>
<li>Creato <code>inertial_mass/debug_modal.js</code> per diagnosticare problemi di visibilità</li>
<li>Funzioni di debug accessibili globalmente: <code>debugModalVisibility()</code>, <code>forceModalVisibility()</code></li>
<li>Test automatico della visibilità quando il modal viene aperto</li>
<li>Observer per monitorare i cambiamenti del modal</li>
</ul>
<h4>5. <strong>File di Test</strong></h4>
<ul>
<li>Creato <code>inertial_mass/test_modal_visibility.html</code> per testare il modal in isolamento</li>
<li>Test di visibilità automatici con report dettagliato</li>
<li>Simulazione dei conflitti CSS per verificare le soluzioni</li>
</ul>
<p><strong>File modificati</strong>:</p>
<ul>
<li><code>inertial_mass/assets/css/modal.css</code> - Specificità massima e regole di emergenza</li>
<li><code>inertial_mass/assets/js/modal.js</code> - Visibilità forzata e debug</li>
<li><code>js/inertial_mass_integration.js</code> - Caricamento debug script</li>
<li><code>inertial_mass/debug_modal.js</code> - Nuovo file di debug</li>
<li><code>inertial_mass/test_modal_visibility.html</code> - Nuovo file di test</li>
</ul>
<p><strong>Risultato</strong>: Modal ora completamente visibile e funzionale anche con tutti i CSS del progetto principale caricati.</p>
<hr />
<h3>🗂️ v2.4.0 (12/12/2024) - Pulizia Documentazione Obsoleta</h3>
<p><strong>Obiettivo</strong>: Ottimizzazione struttura documentazione</p>
<ul>
<li>✅ <strong>Eliminati 8 file obsoleti</strong>: Report fix completati, guide sviluppo superate</li>
<li>✅ <strong>Aggiornati indici</strong>: 00_indice.md, README.md, app_map.md</li>
<li>✅ <strong>Benefici</strong>: -25% file totali, navigazione semplificata, manutenzione ridotta</li>
</ul>
<h3>🔧 v2.3.2 (06/06/2025) - Fix Sistema Log e Diagrammi Mermaid</h3>
<p><strong>Obiettivo</strong>: Risoluzione errori critici sistema</p>
<ul>
<li>✅ <strong>Fix ZipArchive</strong>: Controllo compatibilità con fallback automatico</li>
<li>✅ <strong>Fix JSON AJAX</strong>: Risolto output HTML mescolato con JSON</li>
<li>✅ <strong>Fix Mermaid</strong>: Configurazione migliorata per diagrammi</li>
<li>✅ <strong>Backup automatici</strong>: Creazione backup log prima pulizia</li>
</ul>
<h3>🧹 v2.3.0 (05/06/2025) - Pulizia Completa Workspace</h3>
<p><strong>Obiettivo</strong>: Ottimizzazione struttura progetto</p>
<ul>
<li>✅ <strong>Eliminati file obsoleti</strong>: Test, duplicati, directory non utilizzate</li>
<li>✅ <strong>Creato STRUTTURA_PROGETTO.md</strong>: Documentazione architettura completa</li>
<li>✅ <strong>Performance</strong>: Riduzione complessità, caricamento più veloce</li>
</ul>
<h3>🤖 v2.4.0 (06/06/2025) - Tre Tipologie Costruttive + Fix Validazione</h3>
<p><strong>Obiettivo</strong>: Implementazione tipologie costruttive + risoluzione errori validazione</p>
<ul>
<li>✅ <strong>Tre macro-categorie</strong>: Ponte/Viadotto, Edificio, Edificio Prefabbricato</li>
<li>✅ <strong>Interfaccia dinamica</strong>: Selezione guidata con sottocategorie automatiche</li>
<li>✅ <strong>Parametri specifici</strong>: Pesi, carichi e formule periodo per ogni tipologia</li>
<li>✅ <strong>Fix validazione HTML5</strong>: Risolto errore &quot;invalid form control not focusable&quot;</li>
<li>✅ <strong>Gestione required dinamica</strong>: Attributo required aggiunto/rimosso automaticamente</li>
<li>✅ <strong>Validazione intelligente</strong>: Controlli solo su campi visibili</li>
<li>✅ <strong>Reset completo</strong>: Funzione reset ripristina stato iniziale corretto</li>
<li>✅ <strong>Fix event listener</strong>: Risolto problema sottocategorie non visibili
<ul>
<li>Reinizializzazione forzata dopo apertura modale</li>
<li>Event listener diretto come fallback</li>
<li>Test automatico funzionamento</li>
<li>Debug esteso per troubleshooting</li>
</ul></li>
</ul>
<h3>🤖 v2.3.0 (06/06/2025) - Ottimizzazione Ordine AI + Fattore Smorzamento</h3>
<p><strong>Obiettivo</strong>: Miglioramento performance AI + inclusione fattore smorzamento</p>
<ul>
<li>✅ <strong>Nuovo ordine AI</strong>: Gemma3 → Deepseek → Locale (performance ottimizzate)</li>
<li>✅ <strong>Fattore smorzamento</strong>: Incluso nei calcoli massa inerziale (eta = sqrt(10/(5+xi)))</li>
<li>✅ <strong>Calcolo dinamico</strong>: Recupero smorzamento dall'interfaccia principale ASDP</li>
<li>✅ <strong>Prompt migliorato</strong>: AI utilizza fattore smorzamento specifico del progetto</li>
<li>✅ <strong>Calcolo locale</strong>: Formula NTC 2018 per eta con smorzamento variabile</li>
</ul>
<h3>🤖 v2.2.0 (05/06/2025) - Sistema a Tre Livelli LLM + Fix Parametri Sismici</h3>
<p><strong>Obiettivo</strong>: Affidabilità calcolo massa inerziale + correzione parametri dinamici</p>
<ul>
<li>✅ <strong>Fallback automatico</strong>: Deepseek → Gemma3 → Locale (99.9% affidabilità)</li>
<li>✅ <strong>Fix parametri sismici</strong>: Calcolo TR dinamico basato su VN e Cu utente</li>
<li>✅ <strong>Correzione bug</strong>: Valori TR hardcoded sostituiti con calcolo dinamico</li>
<li>✅ <strong>Interpolazione migliorata</strong>: Griglia sismica con coefficienti SS, CC, ST</li>
<li>✅ <strong>Documentazione centralizzata</strong>: Organizzazione cartella docs/</li>
</ul>
<h3>🎯 v2.1.0 (04/06/2025) - Correzioni Critiche Modulo Massa Inerziale</h3>
<p><strong>Obiettivo</strong>: Risoluzione bug interfaccia</p>
<ul>
<li>✅ <strong>Fix raddoppio icone</strong>: Pulizia HTML dinamica nei risultati</li>
<li>✅ <strong>Ottimizzazione rendering</strong>: Performance migliorate, memory leak risolti</li>
<li>✅ <strong>Test validazione</strong>: Completati con successo</li>
</ul>
<h3>🏗️ v2.0.0 (Dicembre 2024) - Modulo Massa Inerziale</h3>
<p><strong>Obiettivo</strong>: Nuova funzionalità calcolo massa inerziale</p>
<ul>
<li>✅ <strong>Integrazione AI</strong>: Deepseek LLM per analisi avanzata</li>
<li>✅ <strong>Database</strong>: 4 nuove tabelle specializzate</li>
<li>✅ <strong>UI</strong>: Modale responsive con animazioni</li>
<li>✅ <strong>Cache intelligente</strong>: Ottimizzazione performance</li>
</ul>
<h2>🔧 Correzioni Specifiche Implementate</h2>
<h3>Sistema Backup (Giugno 2025)</h3>
<p><strong>Problema</strong>: Errori backup ZIP, dipendenza ZipArchive<br />
<strong>Soluzione</strong>: Sistema backup multiplo con fallback garantito</p>
<ul>
<li>✅ <strong>Metodi multipli</strong>: ZipArchive → PowerShell → 7-Zip → Directory</li>
<li>✅ <strong>Compatibilità universale</strong>: Funziona su qualsiasi ambiente PHP</li>
<li>✅ <strong>Compressione efficace</strong>: Riduzione 75% dimensioni (2.3MB per 175 file)</li>
<li>✅ <strong>Struttura organizzata</strong>: database/ + files/ + backup_info.txt</li>
<li>✅ <strong>Ripristino semplice</strong>: Istruzioni complete incluse</li>
</ul>
<h3>Modulo Massa Inerziale (Giugno 2025)</h3>
<p><strong>Problema</strong>: Dipendenza API esterna, errori connettività, fattore smorzamento mancante<br />
<strong>Soluzione</strong>: Sistema a tre livelli ottimizzato con smorzamento dinamico</p>
<ul>
<li>✅ <strong>Livello 1</strong>: Gemma3 AI (veloce e affidabile, 60% richieste)</li>
<li>✅ <strong>Livello 2</strong>: Deepseek AI (analisi avanzata, 30% richieste)</li>
<li>✅ <strong>Livello 3</strong>: Calcolo locale NTC 2018 (garantito, 10% richieste)</li>
<li>✅ <strong>Fattore smorzamento</strong>: Recupero dinamico dall'interfaccia ASDP</li>
<li>✅ <strong>Formula NTC 2018</strong>: eta = sqrt(10/(5+xi)) &gt;= 0.55 implementata</li>
<li>✅ <strong>Affidabilità</strong>: 99.9% successo, sempre disponibile offline</li>
<li>✅ <strong>Performance</strong>: &lt;1s locale, 2-5s Gemma3, 8-25s Deepseek</li>
</ul>
<h3>Sistema Log (Giugno 2025)</h3>
<p><strong>Problema</strong>: Scroll verticale, visibilità contenuto, layout dispersivo<br />
<strong>Soluzione</strong>: Ristrutturazione completa interfaccia amministrativa</p>
<ul>
<li>✅ <strong>Layout compatto</strong>: Sistema tab Database/File, riduzione -51% spazio</li>
<li>✅ <strong>Scroll ottimizzato</strong>: Calcoli CSS precisi, zero conflitti footer</li>
<li>✅ <strong>UX rivoluzionata</strong>: +125% efficienza spazio, +140% contenuto visibile</li>
<li>✅ <strong>Parser avanzato</strong>: Estrazione timestamp, livello, utente, IP</li>
<li>✅ <strong>Responsive perfetto</strong>: Desktop/tablet/mobile ottimizzati</li>
</ul>
<h3>Parametri Sismici Dinamici (Maggio 2025)</h3>
<p><strong>Problema</strong>: Valori TR hardcoded, parametri utente ignorati nei calcoli<br />
<strong>Soluzione</strong>: Calcolo dinamico completo basato su input utente</p>
<ul>
<li>✅ <strong>TR dinamico</strong>: Calcolo basato su Vita Nominale (VN) e Classe d'Uso (Cu)</li>
<li>✅ <strong>Interpolazione griglia</strong>: Coefficienti SS, CC, ST calcolati dinamicamente</li>
<li>✅ <strong>Parametri utente</strong>: Categoria suolo, topografia, fattore struttura considerati</li>
<li>✅ <strong>Conformità NTC 2018</strong>: Formule e tabelle normative implementate</li>
<li>✅ <strong>Validazione</strong>: Test con valori noti, risultati verificati</li>
</ul>
<h2>🎯 Miglioramenti Proposti (Pianificati)</h2>
<h3>1. Ottimizzazioni Database</h3>
<h3>1.1 Ottimizzazione Tabelle Pesanti</h3>
<p>Dall'analisi delle dimensioni del database sono state identificate le tabelle più pesanti che necessitano di ottimizzazione:</p>
<table>
<thead>
<tr>
<th>Tabella</th>
<th>Righe</th>
<th>Dati (MB)</th>
<th>Indici (MB)</th>
<th>Totale (MB)</th>
</tr>
</thead>
<tbody>
<tr>
<td>zone_sismiche</td>
<td>7,986</td>
<td>1.51</td>
<td>1.40</td>
<td>2.92</td>
</tr>
<tr>
<td>calculation_log</td>
<td>27</td>
<td>2.51</td>
<td>0.01</td>
<td>2.53</td>
</tr>
<tr>
<td>seismic_grid_points</td>
<td>10,604</td>
<td>2.51</td>
<td>0.00</td>
<td>2.51</td>
</tr>
<tr>
<td>comuni</td>
<td>8,016</td>
<td>1.51</td>
<td>0.21</td>
<td>1.73</td>
</tr>
</tbody>
</table>
<h4>Interventi Proposti:</h4>
<ol>
<li>
<p><strong>Ottimizzazione zone_sismiche</strong></p>
<ul>
<li>Rimozione indici ridondanti</li>
<li>Riduzione dimensione campi VARCHAR</li>
<li>Creazione indici composti efficienti</li>
</ul>
</li>
<li>
<p><strong>Gestione calculation_log</strong></p>
<ul>
<li>Implementazione partitioning per data</li>
<li>Policy di retention automatica</li>
<li>Pulizia log obsoleti</li>
</ul>
</li>
<li>
<p><strong>Ottimizzazione seismic_grid_points</strong></p>
<ul>
<li>Aggiunta indici per coordinate</li>
<li>Ottimizzazione precisione decimali</li>
<li>Compressione dati</li>
</ul>
</li>
<li>
<p><strong>Razionalizzazione comuni</strong></p>
<ul>
<li>Ottimizzazione indici di ricerca</li>
<li>Riduzione dimensione campi</li>
<li>Miglioramento query di join</li>
</ul>
</li>
</ol>
<h3>1.2 Gestione Log e Backup</h3>
<ul>
<li>Implementazione rotazione automatica dei log</li>
<li>Backup incrementali giornalieri</li>
<li>Compressione backup storici</li>
<li>Monitoraggio spazio disco</li>
</ul>
<h3>1.3 Query Optimization</h3>
<ul>
<li>Analisi query più frequenti</li>
<li>Creazione viste materializzate</li>
<li>Ottimizzazione join complessi</li>
<li>Cache query frequenti</li>
</ul>
<h2>2. Miglioramenti Performance</h2>
<h3>2.1 Caching</h3>
<ul>
<li>Implementazione Redis per cache applicativa</li>
<li>Caching risultati calcoli sismici</li>
<li>Memorizzazione sessioni utente</li>
<li>Cache per dati statici</li>
</ul>
<h3>2.2 Frontend</h3>
<ul>
<li>Lazy loading componenti pesanti</li>
<li>Ottimizzazione bundle JavaScript</li>
<li>Compressione assets statici</li>
<li>Implementazione Service Worker</li>
</ul>
<h3>2.3 Backend</h3>
<ul>
<li>Implementazione code asincrone</li>
<li>Ottimizzazione elaborazioni batch</li>
<li>Miglioramento gestione sessioni</li>
<li>Logging strutturato</li>
</ul>
<h2>3. Sicurezza</h2>
<h3>3.1 Autenticazione</h3>
<ul>
<li>Implementazione 2FA</li>
<li>Gestione password policy</li>
<li>Blocco tentativi accesso falliti</li>
<li>Audit log accessi</li>
</ul>
<h3>3.2 Autorizzazione</h3>
<ul>
<li>Raffinamento ruoli utente</li>
<li>Controlli accesso granulari</li>
<li>Logging operazioni sensibili</li>
<li>Validazione input avanzata</li>
</ul>
<h2>4. User Experience</h2>
<h3>4.1 Interfaccia Utente</h3>
<ul>
<li>Redesign responsive</li>
<li>Miglioramento accessibilità</li>
<li>Tema dark/light</li>
<li>Customizzazione dashboard</li>
</ul>
<h3>4.2 Funzionalità</h3>
<ul>
<li>Esportazione dati in più formati</li>
<li>Report personalizzabili</li>
<li>Notifiche real-time</li>
<li>Integrazione mappe avanzate</li>
</ul>
<h2>5. Manutenibilità</h2>
<h3>5.1 Codice</h3>
<ul>
<li>Refactoring componenti legacy</li>
<li>Implementazione pattern SOLID</li>
<li>Miglioramento test coverage</li>
<li>Documentazione inline</li>
</ul>
<h3>5.2 Deployment</h3>
<ul>
<li>Pipeline CI/CD</li>
<li>Ambiente staging</li>
<li>Rollback automatico</li>
<li>Monitoraggio errori</li>
</ul>
<h2>6. Piano di Implementazione</h2>
<h3>6.1 Fase 1 - Priorità Alta</h3>
<ol>
<li>Ottimizzazioni database critiche</li>
<li>Implementazione caching</li>
<li>Miglioramenti sicurezza base</li>
</ol>
<h3>6.2 Fase 2 - Priorità Media</h3>
<ol>
<li>Ottimizzazioni frontend</li>
<li>Miglioramenti UX</li>
<li>Implementazione backup avanzati</li>
</ol>
<h3>6.3 Fase 3 - Priorità Bassa</h3>
<ol>
<li>Funzionalità aggiuntive</li>
<li>Miglioramenti estetici</li>
<li>Ottimizzazioni minori</li>
</ol>
<h2>7. Monitoraggio e Manutenzione</h2>
<h3>7.1 Metriche da Monitorare</h3>
<ul>
<li>Tempo risposta query</li>
<li>Utilizzo memoria</li>
<li>Errori applicativi</li>
<li>Performance frontend</li>
</ul>
<h3>7.2 Manutenzione Periodica</h3>
<ul>
<li>Analisi log settimanale</li>
<li>Backup verification</li>
<li>Aggiornamento dipendenze</li>
<li>Test di sicurezza</li>
</ul>
<h2>📊 Analisi Problematiche Identificate (Maggio 2025)</h2>
<h3>Aree di Miglioramento Prioritarie</h3>
<ol>
<li><strong>Funzionalità Incomplete</strong>: Dashboard personalizzata, gestione progetti</li>
<li><strong>Gestione Errori</strong>: Logging semplice, mancanza categorizzazione gravità</li>
<li><strong>Caching</strong>: Calcoli ripetuti, caricamento ridondante risorse</li>
<li><strong>Sicurezza</strong>: Rate limiting, 2FA, token CSRF</li>
<li><strong>Performance</strong>: Lazy loading, ottimizzazione query database</li>
</ol>
<h3>Soluzioni Proposte</h3>
<ul>
<li><strong>Architettura</strong>: Pattern Repository, dependency injection, test coverage</li>
<li><strong>UX</strong>: Feedback visivi, salvataggio automatico, cronologia ricerche</li>
<li><strong>Monitoraggio</strong>: Dashboard performance, analisi errori real-time</li>
</ul>
<h2>📈 Miglioramenti Incrementali (Gennaio 2024)</h2>
<ul>
<li>✅ <strong>Sistema minificazione JavaScript</strong>: Ottimizzazione bundle</li>
<li>✅ <strong>Gestione popup account</strong>: UX migliorata</li>
<li>✅ <strong>Favicon personalizzato</strong>: Branding ASDP</li>
<li>✅ <strong>Gestione errori avanzata</strong>: Logging strutturato</li>
<li>✅ <strong>Autenticazione</strong>: Sistema sicurezza migliorato</li>
</ul>
<h2>🚀 Roadmap Futura (Pianificazioni)</h2>
<h3>Performance &amp; Ottimizzazioni</h3>
<ul>
<li><strong>Frontend</strong>: Lazy loading, bundle splitting, WebP, GZIP</li>
<li><strong>Database</strong>: Query optimization, index tuning, connection pooling</li>
<li><strong>Caching</strong>: Redis, cache headers, statistiche</li>
</ul>
<h3>Sicurezza &amp; Compliance</h3>
<ul>
<li><strong>Autenticazione</strong>: 2FA, rate limiting, CSP</li>
<li><strong>Monitoraggio</strong>: IP sospetti, blocco automatico, audit log</li>
<li><strong>Hardening</strong>: Configurazione server, scanning vulnerabilità</li>
</ul>
<h3>UX/UI Avanzata</h3>
<ul>
<li><strong>Temi</strong>: Dark/light mode, personalizzazione</li>
<li><strong>Accessibilità</strong>: WCAG 2.1, responsive migliorato</li>
<li><strong>Dashboard</strong>: Widget personalizzabili, grafici temporali</li>
</ul>
<h3>Dashboard Amministrativa Avanzata</h3>
<ul>
<li><strong>Statistiche</strong>: Trend accessi, distribuzione geografica, metriche utilizzo</li>
<li><strong>Monitoraggio</strong>: CPU/memoria real-time, notifiche eventi critici</li>
<li><strong>Gestione</strong>: Utenti online, backup programmati, log real-time</li>
</ul>
<h2>Note di Manutenzione</h2>
<ul>
<li>Backup regolari</li>
<li>Test di regressione</li>
<li>Monitoraggio performance</li>
<li>Aggiornamenti sicurezza</li>
</ul>
<h2>Note</h2>
<ul>
<li>Tutte le modifiche devono essere testate in ambiente di sviluppo</li>
<li>Mantenere backup prima di ogni modifica importante</li>
<li>Documentare ogni cambiamento</li>
<li>Seguire le best practices di sicurezza</li>
</ul>
<h1>📋 Registro Aggiornamenti ASDP</h1>
<p>Ultimo aggiornamento: 15/06/2025</p>
<h2>Versione 2.5.0 (15/06/2025) - Ottimizzazione Completa Progetto e Verifica Dati Reali</h2>
<h3>Pulizia Completa File Obsoleti</h3>
<ul>
<li><strong>RIMOSSI: 14 file obsoleti per ottimizzazione progetto</strong>
<ul>
<li><strong>File di test duplicati</strong>: <code>test_dampers.php</code>, <code>test_dampers_simple.php</code>, <code>test_report_generation.html</code></li>
<li><strong>Tools di debug</strong>: <code>simple_backup_test.php</code>, <code>simple_test.php</code>, <code>final_test.php</code>, <code>test_backup_*.php</code></li>
<li><strong>Log temporanei</strong>: <code>debug.log</code>, <code>output.txt</code>, <code>final_output.txt</code>, <code>test_output.html</code></li>
<li><strong>Benefici</strong>: -550KB spazio, struttura più pulita, manutenzione semplificata</li>
<li><strong>Criteri</strong>: Eliminazione duplicazioni, focus su funzionalità essenziali</li>
</ul></li>
</ul>
<h3>Consolidamento File di Test Modulo Massa Inerziale</h3>
<ul>
<li><strong>OTTIMIZZATO: Sistema di test per eliminare duplicazioni</strong>
<ul>
<li><strong>Rimosso</strong>: <code>test_report_generation.html</code> (408 righe) - Funzionalità duplicate</li>
<li><strong>Mantenuto</strong>: <code>test_real_data_flow.html</code> (601 righe) - Test completo flusso dati reali</li>
<li><strong>Motivazione</strong>: Copertura superiore, test dati reali vs simulati, valore futuro</li>
<li><strong>Benefici</strong>: -408 righe, manutenzione semplificata, focus su test significativi</li>
</ul></li>
</ul>
<h3>Verifica Completa Utilizzo Dati Reali</h3>
<ul>
<li><strong>VERIFICATO: Sistema report utilizza esclusivamente dati reali</strong>
<ul>
<li><strong>Problema risolto</strong>: Correzione mapping dati in <code>populateReportTemplate()</code></li>
<li><strong>Flusso validato</strong>: Interfaccia ASDP → Raccolta dati → Memorizzazione → Calcolo AI → Report</li>
<li><strong>Test implementati</strong>: 5 scenari di verifica per validazione dati reali vs hardcoded</li>
<li><strong>Risultato</strong>: Conformità 100% - nessun valore simulato nel report finale</li>
</ul></li>
</ul>
<h3>Documentazione Completa Aggiornata</h3>
<ul>
<li><strong>CREATI: 3 nuovi documenti di analisi</strong>
<ul>
<li><code>file_obsoleti_analisi.md</code> - Analisi dettagliata pulizia progetto</li>
<li><code>analisi_file_test_massa_inerziale.md</code> - Valutazione consolidamento test</li>
<li><code>verifica_dati_reali_v2.5.0.md</code> - Documentazione verifica conformità</li>
</ul></li>
<li><strong>AGGIORNATI: Tutti i file di documentazione</strong>
<ul>
<li><code>app_map.md</code> - Struttura progetto finale ottimizzata</li>
<li><code>11_miglioramenti.md</code> - Registro completo modifiche</li>
</ul></li>
</ul>
<h3>Miglioramenti Architetturali</h3>
<ul>
<li><strong>Struttura ottimizzata</strong>: Solo file essenziali mantenuti</li>
<li><strong>Test significativi</strong>: Focus su verifica dati reali</li>
<li><strong>Manutenzione semplificata</strong>: Eliminazione ridondanze</li>
<li><strong>Documentazione coerente</strong>: Riflette struttura finale</li>
</ul>
<h3>File Modificati</h3>
<ul>
<li><strong>Rimossi</strong>: 14 file obsoleti (test, debug, log temporanei)</li>
<li><strong>Consolidati</strong>: File di test modulo massa inerziale</li>
<li><strong>Corretti</strong>: Mapping dati in <code>inertial_mass/assets/js/modal.js</code></li>
<li><strong>Aggiornati</strong>: Tutta la documentazione progetto</li>
</ul>
<h2>Versione 2.4.2 (14/06/2025) - Miglioramenti UI Modulo Massa Inerziale</h2>
<h3>Interfaccia Utente Modale Massa Inerziale</h3>
<ul>
<li>
<p><strong>RISOLTO: Doppia icona nel titolo risultati</strong></p>
<ul>
<li>Problema: Il titolo &quot;Distribuzione Forze per Piano&quot; nel modale di massa inerziale (<code>inertial_mass/modal.php</code>) mostrava un'icona 📊 duplicata.</li>
<li>Soluzione: L'icona hard-coded è stata rimossa dalla funzione <code>generateResultsHTML</code> nel file <code>inertial_mass/assets/js/modal.js</code>. La visualizzazione dell'icona è ora gestita esclusivamente tramite CSS, assicurando una singola occorrenza.</li>
<li>File modificato: <code>inertial_mass/assets/js/modal.js</code>.</li>
</ul>
</li>
<li>
<p><strong>UNIFORMATO: Stile pulsanti del modale</strong></p>
<ul>
<li>Obiettivo: Garantire coerenza visiva e un aspetto professionale per i pulsanti di interazione principali all'interno del modale.</li>
<li>Modifica: I seguenti pulsanti nel file <code>inertial_mass/modal.php</code> sono stati aggiornati per utilizzare la classe <code>btn btn-primary</code> (stile primario arancione):</li>
<li>&quot;Nuovo Calcolo&quot; (precedentemente <code>btn-secondary</code>).</li>
<li>&quot;+ Aggiungi Piano&quot; (precedentemente con classe custom <code>btn-add-floor</code>).</li>
<li>&quot;Annulla&quot; (precedentemente <code>btn-secondary</code>).</li>
<li>Risultato: Tutti i pulsanti principali del modale ora presentano uno stile uniforme.</li>
<li>File modificato: <code>inertial_mass/modal.php</code>.</li>
</ul>
</li>
</ul>
<h3>Documentazione Correlata</h3>
<ul>
<li>Aggiornato il file <code>inertial_mass/in_mass.md</code> con una sezione dedicata a questi miglioramenti dell'interfaccia utente.</li>
</ul>
<h2>Versione 2.4.1 (12/12/2024) - Consolidamento Documentazione</h2>
<h3>Unificazione Completa in 11_miglioramenti.md</h3>
<ul>
<li><strong>CONSOLIDATI: 5 file di documentazione tecnica</strong>
<ul>
<li><code>backup_system_final_solution.md</code> - Soluzione sistema backup (205 righe → sezione concisa)</li>
<li><code>CORREZIONE_MASSA_INERZIALE.md</code> - Correzioni modulo massa inerziale (169 righe → sezione concisa)</li>
<li><code>SISTEMA_TRE_LIVELLI_LLM.md</code> - Sistema fallback LLM (215 righe → sezione concisa)</li>
<li><code>RICALCOLO_PARAMETRI_SISMICI.md</code> - Fix parametri dinamici (integrato)</li>
<li><code>pulizia_documentazione_report.md</code> - Report pulizia precedente (integrato)</li>
</ul></li>
</ul>
<h3>Ristrutturazione 11_miglioramenti.md</h3>
<ul>
<li><strong>ORGANIZZAZIONE CRONOLOGICA</strong>: Aggiornamenti dal più recente al più vecchio</li>
<li><strong>DESCRIZIONI CONCISE</strong>: Informazioni essenziali mantenute, dettagli tecnici ridotti</li>
<li><strong>SEZIONI UNIFICATE</strong>:
<ul>
<li>📋 Aggiornamenti Implementati (cronologico)</li>
<li>🔧 Correzioni Specifiche (dettagliate)</li>
<li>🎯 Miglioramenti Proposti (pianificazioni future)</li>
</ul></li>
</ul>
<h3>Aggiornamenti Strutturali</h3>
<ul>
<li><strong>AGGIORNATI: File indice e navigazione</strong>
<ul>
<li><code>00_indice.md</code> - Rimossi 5 riferimenti consolidati</li>
<li><code>README.md</code> - Semplificata struttura documentazione</li>
<li><code>app_map.md</code> - Aggiornata mappa con consolidamento</li>
<li><code>12_aggiornamenti.md</code> - Documentato consolidamento</li>
</ul></li>
</ul>
<h3>Benefici Ottenuti</h3>
<ul>
<li>✅ <strong>Documentazione centralizzata</strong>: Un solo file per tutti gli aggiornamenti</li>
<li>✅ <strong>Navigazione semplificata</strong>: Meno file da consultare</li>
<li>✅ <strong>Manutenzione unificata</strong>: Aggiornamenti in un solo posto</li>
<li>✅ <strong>Cronologia chiara</strong>: Ordine temporale dal più recente</li>
<li>✅ <strong>Informazioni concise</strong>: Focus su aspetti essenziali</li>
</ul>
<h3>File Modificati</h3>
<ul>
<li><code>docs/11_miglioramenti.md</code> - Ristrutturato completamente con contenuti consolidati</li>
<li><code>docs/00_indice.md</code> - Rimossi riferimenti file consolidati</li>
<li><code>docs/README.md</code> - Semplificata struttura documentazione</li>
<li><code>docs/app_map.md</code> - Aggiornata mappa applicazione</li>
<li><code>docs/12_aggiornamenti.md</code> - Documentato consolidamento</li>
</ul>
<h2>Versione 2.4.0 (12/12/2024) - Pulizia Documentazione Obsoleta</h2>
<h3>Ottimizzazione Documentazione</h3>
<ul>
<li><strong>ELIMINATI: 8 file di documentazione obsoleti</strong>
<ul>
<li><code>backup_system_fix_report.md</code> - Report fix backup ZIP (problema risolto definitivamente)</li>
<li><code>logs_scroll_definitive_fix.md</code> - Fix scroll verticale (implementato e funzionante)</li>
<li><code>logs_scroll_fix.md</code> - Fix scroll precedente (superato dalla versione definitiva)</li>
<li><code>logs_visibility_fix.md</code> - Fix visibilità contenuto log (implementato)</li>
<li><code>logs_layout_restructure.md</code> - Ristrutturazione layout log (completata)</li>
<li><code>logs_system_improvements.md</code> - Miglioramenti sistema log (implementati)</li>
<li><code>sviluppo_inerziale.md</code> - Guida sviluppo modulo massa inerziale (modulo completato)</li>
<li><code>database_massa_inerziale_report.md</code> - Report database (informazioni migrate in 14_massa_inerziale.md)</li>
</ul></li>
</ul>
<h3>Aggiornamenti Strutturali</h3>
<ul>
<li><strong>AGGIORNATI: File indice e mappa</strong>
<ul>
<li><code>00_indice.md</code> - Rimossi riferimenti ai file eliminati</li>
<li><code>README.md</code> - Aggiornata struttura documentazione</li>
<li><code>app_map.md</code> - Aggiornata mappa applicazione</li>
<li>Aggiornate date ultimo aggiornamento</li>
</ul></li>
</ul>
<h3>Benefici Ottenuti</h3>
<ul>
<li>✅ <strong>Documentazione più pulita</strong>: Focus sui file essenziali e correnti</li>
<li>✅ <strong>Navigazione semplificata</strong>: Meno confusione tra file obsoleti e attuali</li>
<li>✅ <strong>Manutenzione ridotta</strong>: Meno file da aggiornare e mantenere</li>
<li>✅ <strong>Struttura ottimizzata</strong>: Organizzazione logica e coerente</li>
<li>✅ <strong>Performance migliorate</strong>: Caricamento più veloce della documentazione</li>
</ul>
<h3>File Modificati</h3>
<ul>
<li><code>docs/00_indice.md</code> - Aggiornato indice generale</li>
<li><code>docs/README.md</code> - Aggiornata panoramica documentazione</li>
<li><code>docs/app_map.md</code> - Aggiornata mappa applicazione</li>
<li><code>docs/12_aggiornamenti.md</code> - Documentata pulizia</li>
</ul>
<h2>Versione 2.3.2 (06/06/2025) - Fix Sistema Log e Diagrammi Mermaid</h2>
<h3>Correzioni Critiche</h3>
<ul>
<li>
<p><strong>RISOLTO: Errore 500 nella pulizia log</strong></p>
<ul>
<li>Problema: Fatal error &quot;Class ZipArchive not found&quot; in <code>admin/clear_logs.php</code></li>
<li>Causa: Estensione PHP ZIP non disponibile in ambiente XAMPP</li>
<li>Soluzione: Implementato controllo compatibilità con fallback automatico</li>
<li>Se ZipArchive disponibile: crea backup ZIP compresso</li>
<li>Se ZipArchive non disponibile: salva backup come directory</li>
<li>Benefici: Funzionalità pulizia log sempre operativa indipendentemente dalla configurazione PHP</li>
<li>File modificato: <code>admin/clear_logs.php</code></li>
</ul>
</li>
<li>
<p><strong>RISOLTO: JSON invalido nelle risposte AJAX</strong></p>
<ul>
<li>Problema: Output HTML mescolato con JSON durante pulizia log</li>
<li>Causa: Errore PHP interrompeva esecuzione prima della risposta JSON</li>
<li>Soluzione: Risolto automaticamente con fix ZipArchive</li>
<li>Benefici: Interfaccia utente riceve sempre risposte JSON valide</li>
</ul>
</li>
<li>
<p><strong>RISOLTO: Errore JavaScript nei diagrammi Mermaid</strong></p>
<ul>
<li>Problema: &quot;Could not find a suitable point for the given distance&quot; in <code>docs/relazione_tecnica.html</code></li>
<li>Causa: Configurazione problematica e connessioni multiple complesse</li>
<li>Soluzione:</li>
<li>Migliorata configurazione Mermaid (curve linear, htmlLabels false, spacing aumentato)</li>
<li>Semplificate connessioni multiple nel diagramma flowchart</li>
<li>Aggiunto padding e specificato renderer 'dagre'</li>
<li>Benefici: Diagrammi si renderizzano correttamente senza errori JavaScript</li>
<li>File modificato: <code>docs/relazione_tecnica.html</code></li>
</ul>
</li>
</ul>
<h3>Miglioramenti Sistema</h3>
<ul>
<li><strong>Backup automatici log</strong>: Creazione backup nella cartella <code>backups/logs_*</code> prima della pulizia</li>
<li><strong>Compatibilità estesa</strong>: Sistema funziona con qualsiasi configurazione PHP</li>
<li><strong>Stabilità diagrammi</strong>: Rendering Mermaid più robusto e affidabile</li>
<li><strong>Documentazione aggiornata</strong>: Troubleshooting esteso con nuovi fix</li>
</ul>
<h3>File Modificati</h3>
<ul>
<li><code>admin/clear_logs.php</code> - Controllo compatibilità ZipArchive</li>
<li><code>docs/relazione_tecnica.html</code> - Configurazione e diagramma Mermaid</li>
<li><code>docs/07_troubleshooting.md</code> - Nuove sezioni problemi log e Mermaid</li>
<li><code>docs/app_map.md</code> - Registro modifiche aggiornato</li>
</ul>
<h2>Versione 2.3.1 (05/06/2025) - Correzione Visualizzazione Versione nel Footer</h2>
<h3>Correzioni</h3>
<ul>
<li><strong>RISOLTO: Mancata visualizzazione della versione corretta nel footer.</strong>
<ul>
<li>Problema: Il footer mostrava una versione statica o obsoleta (v1.0.0) invece della versione corrente definita in <code>docs/12_aggiornamenti.md</code>.</li>
<li>Causa 1: <code>includes/components/footer.php</code> non era implementato per mostrare la versione dinamica.</li>
<li>Causa 2: L'espressione regolare in <code>includes/VersionManager.php</code> non estraeva correttamente la versione dal formato attuale di <code>docs/12_aggiornamenti.md</code>.</li>
<li>Causa 3: La cache della versione (<code>cache/version.txt</code>, <code>includes/cache/version_fallback.txt</code>) non veniva invalidata correttamente dopo le modifiche, mantenendo la vecchia versione.</li>
<li>Soluzione:</li>
<li>Modificato <code>includes/VersionManager.php</code> per utilizzare una regex corretta (<code>/^## Versione\s+([0-9.]+)\s+\(/m</code>) che legge la prima versione listata come la corrente.</li>
<li>Modificato <code>includes/components/footer.php</code> per includere <code>VersionManager</code>, recuperare la versione e visualizzarla.</li>
<li>Chiarita la necessità di pulire la cache (manualmente o tramite <code>tools/refresh_version.php</code>) dopo tali modifiche per vedere l'aggiornamento.</li>
<li>File modificati: <code>includes/VersionManager.php</code>, <code>includes/components/footer.php</code>.</li>
</ul></li>
</ul>
<h2>Versione 2.3.0 (05/06/2025) - Pulizia Completa Workspace e Ottimizzazione Struttura</h2>
<h3>Pulizia Workspace Completa</h3>
<ul>
<li>
<p><strong>ELIMINATI: File obsoleti e duplicati</strong></p>
<ul>
<li><code>admin/backup_process.php.deprecated</code> - File di backup obsoleto</li>
<li><code>api/backup_process.php</code> e <code>api/backup_process_alternative.php</code> - Duplicati non utilizzati</li>
<li><code>includes/cache_service.php</code> - Duplicato di <code>includes/services/CacheService.php</code></li>
<li><code>includes/services/Logger.php</code> - Duplicato di <code>includes/logger.php</code></li>
<li><code>includes/autoload.php</code> - Autoloader personalizzato non utilizzato</li>
</ul>
</li>
<li>
<p><strong>RIMOSSI: File di test non necessari</strong></p>
<ul>
<li><code>admin/test_backup_*.php</code> - File di test backup obsoleti</li>
<li><code>inertial_mass/test_*.php</code> e <code>test_*.html</code> - File di test modulo massa inerziale</li>
<li><code>test_*.php</code> (root) - File di test temporanei</li>
<li><code>phpunit.xml</code> - Configurazione test non più necessaria</li>
</ul>
</li>
<li>
<p><strong>ELIMINATE: Directory non utilizzate</strong></p>
<ul>
<li><code>src/</code> - Struttura namespace ASDP non utilizzata nel codice principale</li>
<li><code>tests/</code> - Directory test dipendente da src/ eliminata</li>
<li><code>spaceinv/</code> - Gioco non correlato al progetto</li>
<li><code>tools/debug/</code> - File di debug non necessari in produzione</li>
</ul>
</li>
<li>
<p><strong>PULITI: File temporanei e cache</strong></p>
<ul>
<li><code>inertial_mass/cache/*.json</code> - File cache temporanei</li>
<li><code>logs/*.log</code> - File di log per ambiente pulito</li>
<li><code>docs/nextsession.md</code>, <code>docs/timesheet.md</code> - Note temporanee di sviluppo</li>
</ul>
</li>
</ul>
<h3>Miglioramenti Struttura</h3>
<ul>
<li><strong>CREATO: STRUTTURA_PROGETTO.md</strong> - Documentazione completa architettura</li>
<li><strong>AGGIORNATO: Indice documentazione</strong> - Rimossi riferimenti file eliminati</li>
<li><strong>OTTIMIZZATA: Organizzazione directory</strong> - Struttura più pulita e navigabile</li>
</ul>
<h3>Benefici Ottenuti</h3>
<ul>
<li>✅ <strong>Riduzione complessità</strong>: Eliminati file duplicati e obsoleti</li>
<li>✅ <strong>Performance migliorate</strong>: Meno file da caricare e processare</li>
<li>✅ <strong>Manutenibilità</strong>: Struttura più chiara e comprensibile</li>
<li>✅ <strong>Documentazione</strong>: Architettura completamente documentata</li>
<li>✅ <strong>Pulizia</strong>: Ambiente di sviluppo ottimizzato</li>
</ul>
<h2>Versione 2.2.0 (05/06/2025) - Sistema a Tre Livelli LLM e Correzioni Parametri Sismici</h2>
<h3>Nuove Funzionalità Principali</h3>
<ul>
<li><strong>IMPLEMENTATO: Sistema a Tre Livelli LLM</strong>
<ul>
<li>Livello 1: Deepseek AI (primario) - Analisi ingegneristica avanzata</li>
<li>Livello 2: Google Gemma3 (fallback) - Modello compatto e veloce</li>
<li>Livello 3: Calcolo Locale (garantito) - Formule NTC 2018 standard</li>
<li>Fallback automatico trasparente tra provider</li>
<li>Logging dettagliato per monitoraggio</li>
<li>Affidabilità 99.9% garantita</li>
</ul></li>
</ul>
<h3>Correzioni Critiche</h3>
<ul>
<li><strong>RISOLTO: Bug parametri sismici dinamici</strong>
<ul>
<li>Problema: Parametri utente (vita nominale, classe d'uso, categoria suolo, etc.) ignorati nei calcoli</li>
<li>Causa: Valori TR hardcoded invece di calcolo dinamico</li>
<li>Soluzione: Implementato calcolo TR dinamico basato su parametri utente</li>
<li>File modificato: <code>api/calculate_seismic_params.php</code></li>
</ul></li>
</ul>
<h3>Miglioramenti Architetturali</h3>
<ul>
<li><strong>Configurazione Gemma3</strong>: Modello <code>gemma-2-2b-it</code> per velocità e efficienza</li>
<li><strong>Gestione errori robusta</strong>: Retry logic e timeout configurabili</li>
<li><strong>API unificata</strong>: Interfaccia comune per tutti i provider LLM</li>
<li><strong>Documentazione completa</strong>: Sistema a tre livelli documentato</li>
</ul>
<h3>Organizzazione Progetto</h3>
<ul>
<li><strong>Documentazione centralizzata</strong>: Tutti i file .md spostati in cartella <code>docs/</code></li>
<li><strong>Indice aggiornato</strong>: Nuovi documenti inclusi nell'indice generale</li>
<li><strong>Pulizia file temporanei</strong>: Rimossi file di test non necessari</li>
</ul>
<h2>Versione 2.1.0 (04/06/2025) - Correzioni Critiche Modulo Massa Inerziale</h2>
<h3>Correzioni Critiche</h3>
<ul>
<li><strong>RISOLTO: Raddoppio icone nei risultati</strong>
<ul>
<li>Problema: Icone duplicate nei titoli delle sezioni (📊📊 Distribuzione Forze, 🤖🤖 Analisi AI)</li>
<li>Causa: Mancanza di pulizia HTML nel rendering dinamico</li>
<li>Soluzione: Implementata pulizia contenuto esistente in <code>displayResults()</code></li>
<li>File modificato: <code>inertial_mass/assets/js/modal.js</code></li>
</ul></li>
</ul>
<h3>Miglioramenti UI/UX</h3>
<ul>
<li><strong>Ottimizzate icone tabella</strong>: 🏢 PIANO, ⚖️ MASSA (T), 📏 ALTEZZA (M), ⭐ FORZA (KN)</li>
<li><strong>Migliorato sistema di logging</strong>: Debug avanzato per troubleshooting</li>
<li><strong>Performance rendering</strong>: Ridotto tempo di visualizzazione risultati</li>
<li><strong>Prevenzione memory leak</strong>: Eliminazione duplicazioni HTML</li>
</ul>
<h3>Aggiornamenti Documentazione</h3>
<ul>
<li>Aggiornato README.md principale con panoramica completa progetto</li>
<li>Aggiornato README.md modulo massa inerziale con bug fix</li>
<li>Aggiornata documentazione tecnica (docs/14_massa_inerziale.md)</li>
<li>Aggiornato troubleshooting (docs/07_troubleshooting.md)</li>
</ul>
<h3>Test e Validazione</h3>
<ul>
<li>✅ Test raddoppio icone: Risoluzione confermata</li>
<li>✅ Test scroll verticale: Funzionamento verificato</li>
<li>✅ Test performance: Rendering ottimizzato</li>
<li>✅ Test cross-browser: Compatibilità mantenuta</li>
</ul>
<h2>Versione 2.0.0 (Dicembre 2024) - Modulo Massa Inerziale</h2>
<h3>Nuove Funzionalità</h3>
<ul>
<li><strong>Modulo Massa Inerziale completo</strong>
<ul>
<li>Integrazione AI con Deepseek LLM</li>
<li>Calcolo automatico massa inerziale sismica</li>
<li>Interfaccia modale responsive</li>
<li>Sistema di cache intelligente</li>
<li>Rate limiting per sicurezza API</li>
</ul></li>
</ul>
<h3>Integrazioni</h3>
<ul>
<li><strong>Database</strong>: 4 nuove tabelle per massa inerziale</li>
<li><strong>API</strong>: Servizi LLM, data service, save results</li>
<li><strong>UI</strong>: Modale scrollabile con animazioni</li>
<li><strong>Sicurezza</strong>: Autenticazione e validazione input</li>
</ul>
<h2>Versione 1.1.0 (20/01/2024)</h2>
<h3>Aggiunte</h3>
<ul>
<li>Implementato nuovo sistema di cache</li>
<li>Aggiunta cartella backups per gestione backup</li>
<li>Creata documentazione API aggiornata</li>
<li>Implementato logging avanzato</li>
</ul>
<h3>Modifiche</h3>
<ul>
<li>Ottimizzato sistema di logging</li>
<li>Aggiornati file di configurazione</li>
<li>Migliorata struttura della documentazione</li>
<li>Aggiornato sistema di build</li>
</ul>
<h3>Correzioni</h3>
<ul>
<li>Risolti problemi di performance</li>
<li>Corretti errori nella documentazione</li>
<li>Sistemati link nella documentazione</li>
<li>Risolti problemi di cache</li>
</ul>
<h2>Versione 1.0.9 (15/01/2024)</h2>
<h3>Aggiunte</h3>
<ul>
<li>Implementato sistema di report</li>
<li>Aggiunto tooltip per funzionalità</li>
<li>Creata nuova documentazione tecnica</li>
</ul>
<h3>Modifiche</h3>
<ul>
<li>Ottimizzato calcolo sismico</li>
<li>Migliorata gestione errori</li>
<li>Aggiornata documentazione</li>
</ul>
<h3>Correzioni</h3>
<ul>
<li>Risolti bug minori</li>
<li>Corretti errori di battitura</li>
<li>Sistemati problemi di layout</li>
</ul>
<h2>Versione 1.0.8 (10/01/2024)</h2>
<h3>Aggiunte</h3>
<ul>
<li>Nuove funzionalità di ricerca</li>
<li>Sistema di notifiche</li>
<li>Backup automatico</li>
</ul>
<h3>Modifiche</h3>
<ul>
<li>Migliorata interfaccia utente</li>
<li>Ottimizzate query database</li>
<li>Aggiornata struttura file</li>
</ul>
<h2>Registro Aggiornamenti</h2>
<ul>
<li><strong>15/06/2025</strong>: <strong>OTTIMIZZAZIONE COMPLETA PROGETTO v2.5.0</strong> - Pulizia sistematica 14 file obsoleti, consolidamento test modulo massa inerziale, verifica completa utilizzo dati reali. Struttura finale ottimizzata con -550KB, eliminazione duplicazioni, documentazione completa aggiornata. Progetto pronto per sviluppi futuri.</li>
<li><strong>12/12/2024</strong>: <strong>CONSOLIDAMENTO DOCUMENTAZIONE v2.4.1</strong> - Unificati 5 file tecnici in 11_miglioramenti.md. Struttura cronologica, descrizioni concise, navigazione centralizzata. Documentazione completamente ottimizzata.</li>
<li><strong>12/12/2024</strong>: <strong>PULIZIA DOCUMENTAZIONE v2.4.0</strong> - Eliminati 8 file di documentazione obsoleti (report fix completati, guide sviluppo superate). Aggiornati indici e mappe. Struttura documentazione ottimizzata per manutenibilità e navigazione.</li>
<li><strong>05/06/2025</strong>: <strong>PULIZIA WORKSPACE v2.3.0</strong> - Eliminati file obsoleti, duplicati e di test. Riorganizzata struttura progetto. Creato STRUTTURA_PROGETTO.md completo. Ottimizzate performance e manutenibilità.</li>
<li><strong>05/06/2025</strong>: <strong>SISTEMA TRE LIVELLI LLM v2.2.0</strong> - Implementato sistema fallback Deepseek → Gemma3 → Locale per calcolo massa inerziale. Corretti bug parametri sismici dinamici. Organizzata documentazione in cartella docs. Affidabilità 99.9% garantita.</li>
<li><strong>04/06/2025</strong>: <strong>CORREZIONI CRITICHE v2.1.0</strong> - Risolto problema raddoppio icone nel modulo massa inerziale. Implementata pulizia HTML dinamica, ottimizzate performance rendering, aggiornata documentazione completa. Test di validazione completati con successo.</li>
<li><strong>07/05/2025</strong>: Completata analisi approfondita del sistema. Identificate problematiche principali (funzionalità incomplete, gestione errori basilare, mancanza caching efficiente, sicurezza migliorabile, performance ottimizzabile) e proposte soluzioni. Aggiornata documentazione con nuove priorità di sviluppo.</li>
<li><strong>27/04/2025</strong>: Pulizia completa del sistema. Svuotate tutte le tabelle temporanee, log, backup dal database e rimossi tutti i file temporanei/log dalle cartelle <code>backups</code>, <code>logs</code>, <code>cache</code>, <code>includes/logs</code>. Ambiente riportato allo stato pulito per una nuova sessione di lavoro.</li>
</ul>
<h2>Prossimi Aggiornamenti Pianificati</h2>
<ol>
<li>Test approfonditi interfaccia massa inerziale con sistema a tre livelli</li>
<li>Dashboard personalizzata</li>
<li>Nuova valutazione guidata</li>
<li>Storico valutazioni</li>
<li>Gestione progetti</li>
<li>Sistema di notifiche avanzato</li>
<li>Ottimizzazione cache per calcoli LLM</li>
</ol>
<h2>Note di Rilascio</h2>
<ul>
<li>Testare sempre in ambiente di sviluppo</li>
<li>Backup database prima degli aggiornamenti</li>
<li>Verificare compatibilità browser</li>
<li>Controllare log dopo aggiornamenti</li>
</ul>
<h1>13. Flussi di Lavoro Comuni</h1>
<h2>1. Nuovo Calcolo Sismico</h2>
<h3>Accesso Dashboard</h3>
<pre><code class="language-php">// Verifica sessione
session_start();
if (!isset($_SESSION['user_id'])) {
    header('Location: /login.php');
    exit;
}

// Verifica permessi
$user = new User($db);
if (!$user-&gt;hasPermission('calculate_seismic')) {
    throw new PermissionException('Accesso non autorizzato');
}</code></pre>
<h3>Selezione Punto</h3>
<pre><code class="language-javascript">// Inizializzazione mappa
const map = L.map('map').setView([41.9028, 12.4964], 6);
L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png').addTo(map);

// Click sulla mappa
map.on('click', async e =&gt; {
    const { lat, lng } = e.latlng;

    // Aggiorna marker
    if (currentMarker) map.removeLayer(currentMarker);
    currentMarker = L.marker([lat, lng]).addTo(map);

    // Recupera informazioni punto
    const info = await getPointInfo(lat, lng);
    updateInfoPanel(info);
});</code></pre>
<h3>Calcolo Parametri</h3>
<pre><code class="language-javascript">// Form submit
document.getElementById('calcForm').addEventListener('submit', async e =&gt; {
    e.preventDefault();

    // Raccolta dati
    const data = {
        lat: document.getElementById('lat').value,
        lon: document.getElementById('lon').value,
        vitaNominale: document.getElementById('vn').value,
        classeUso: document.getElementById('cu').value,
        categoriaTerreno: document.getElementById('ct').value,
        categoriaTopografica: document.getElementById('ctop').value
    };

    try {
        // Calcolo
        const result = await calculateSeismicParams(data);

        // Visualizza risultati
        showResults(result);

        // Salva storico
        saveCalculation(result);
    } catch (error) {
        showError('Errore nel calcolo: ' + error.message);
    }
});</code></pre>
<h2>2. Gestione Backup</h2>
<h3>Backup Database</h3>
<pre><code class="language-powershell"># Script backup.ps1
$date = Get-Date -Format "yyyyMMdd"
$backupPath = "C:\xampp\htdocs\asdp\backup"
$dbName = "asdp_db"

# Backup database
mysqldump -u root -p $dbName &gt; "$backupPath\db_$date.sql"

# Compressione
Compress-Archive -Path "$backupPath\db_$date.sql" -DestinationPath "$backupPath\db_$date.zip"

# Pulizia vecchi backup
Get-ChildItem $backupPath -Filter "db_*.zip" | 
    Where-Object { $_.CreationTime -lt (Get-Date).AddDays(-30) } |
    Remove-Item</code></pre>
<h3>Backup File</h3>
<pre><code class="language-powershell"># Script backup_files.ps1
$date = Get-Date -Format "yyyyMMdd"
$sourcePath = "C:\xampp\htdocs\asdp"
$backupPath = "C:\backup\asdp"

# Cartelle da escludere
$exclude = @(
    "node_modules",
    "vendor",
    "temp",
    "logs"
)

# Backup incrementale
robocopy $sourcePath "$backupPath\$date" /MIR /XD $exclude /LOG:"$backupPath\backup_$date.log"</code></pre>
<h3>Restore</h3>
<pre><code class="language-powershell"># Script restore.ps1
param(
    [string]$backupDate,
    [string]$backupType
)

if ($backupType -eq "db") {
    # Restore database
    mysql -u root -p asdp_db &lt; "backup\db_$backupDate.sql"
} elseif ($backupType -eq "files") {
    # Restore files
    robocopy "C:\backup\asdp\$backupDate" "C:\xampp\htdocs\asdp" /E /IS /IT
}</code></pre>
<h2>3. Gestione Errori Comuni</h2>
<h3>Database Connection</h3>
<pre><code class="language-php">// Gestione connessione
try {
    $db = new PDO(
        "mysql:host={$config['db_host']};dbname={$config['db_name']}",
        $config['db_user'],
        $config['db_pass']
    );
    $db-&gt;setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch (PDOException $e) {
    // Log errore
    error_log("DB Connection Error: " . $e-&gt;getMessage());

    // Notifica admin
    $mailer = new Mailer();
    $mailer-&gt;sendAlert(
        "Database Error",
        "Connection failed: " . $e-&gt;getMessage()
    );

    // Mostra errore utente
    throw new DatabaseException("Errore di connessione al database");
}</code></pre>
<h3>File Permissions</h3>
<pre><code class="language-php">// Verifica permessi
function checkPermissions($path) {
    $required = [
        'logs' =&gt; 0777,
        'temp' =&gt; 0777,
        'uploads' =&gt; 0777
    ];

    foreach ($required as $dir =&gt; $perm) {
        $fullPath = $path . DIRECTORY_SEPARATOR . $dir;
        $currentPerm = fileperms($fullPath) &amp; 0777;

        if ($currentPerm !== $perm) {
            throw new PermissionException(
                "Permessi errati per $dir: " . 
                decoct($currentPerm) . " invece di " . decoct($perm)
            );
        }
    }
}</code></pre>
<h3>API Errors</h3>
<pre><code class="language-php">// Middleware gestione errori
class ErrorMiddleware {
    public function handle($request, $next) {
        try {
            return $next($request);
        } catch (ValidationException $e) {
            return $this-&gt;error(400, $e-&gt;getMessage());
        } catch (AuthException $e) {
            return $this-&gt;error(401, $e-&gt;getMessage());
        } catch (NotFoundException $e) {
            return $this-&gt;error(404, $e-&gt;getMessage());
        } catch (Exception $e) {
            error_log($e-&gt;getMessage());
            return $this-&gt;error(500, "Errore interno del server");
        }
    }

    private function error($code, $message) {
        http_response_code($code);
        return ['error' =&gt; true, 'message' =&gt; $message];
    }
}</code></pre>
<h2>4. Manutenzione Periodica</h2>
<h3>Giornaliera</h3>
<pre><code class="language-powershell"># Script daily_maintenance.ps1

# 1. Pulizia log
Get-ChildItem "logs" -Filter "*.log" |
    Where-Object { $_.LastWriteTime -lt (Get-Date).AddDays(-7) } |
    Remove-Item

# 2. Verifica spazio
$drive = Get-PSDrive C
if ($drive.Free/1GB -lt 5) {
    Send-MailMessage -Subject "Spazio disco insufficiente"
}

# 3. Backup
.\backup.ps1</code></pre>
<h3>Settimanale</h3>
<pre><code class="language-sql">-- Script weekly_maintenance.sql

-- 1. Ottimizzazione tabelle
OPTIMIZE TABLE users, comuni, catasto_info;

-- 2. Analisi tabelle
ANALYZE TABLE users, comuni, catasto_info;

-- 3. Pulizia dati temporanei
DELETE FROM temp_calculations WHERE created_at &lt; DATE_SUB(NOW(), INTERVAL 7 DAY);</code></pre>
<h3>Mensile</h3>
<pre><code class="language-php">// Script monthly_maintenance.php

// 1. Verifica integrità
function checkIntegrity() {
    global $db;

    // Check foreign keys
    $stmt = $db-&gt;query("
        SELECT TABLE_NAME, CONSTRAINT_NAME
        FROM information_schema.TABLE_CONSTRAINTS
        WHERE CONSTRAINT_TYPE = 'FOREIGN KEY'
    ");

    while ($row = $stmt-&gt;fetch()) {
        // Verifica vincoli
        $db-&gt;query("
            ALTER TABLE {$row['TABLE_NAME']}
            CHECK CONSTRAINT {$row['CONSTRAINT_NAME']}
        ");
    }
}

// 2. Report statistiche
function generateStats() {
    global $db;

    $stats = [
        'users' =&gt; $db-&gt;query("SELECT COUNT(*) FROM users")-&gt;fetchColumn(),
        'calculations' =&gt; $db-&gt;query("SELECT COUNT(*) FROM calculations")-&gt;fetchColumn(),
        'disk_usage' =&gt; disk_free_space('/') / disk_total_space('/') * 100
    ];

    // Invia report
    $mailer = new Mailer();
    $mailer-&gt;sendReport('Monthly Stats', $stats);
}</code></pre>
<h2>5. Best Practices</h2>
<h3>Logging</h3>
<pre><code class="language-php">// Logger configurabile
class Logger {
    private static $instance;
    private $handlers = [];

    public static function getInstance() {
        if (!self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    public function addHandler($handler) {
        $this-&gt;handlers[] = $handler;
    }

    public function log($level, $message, array $context = []) {
        $entry = [
            'timestamp' =&gt; date('Y-m-d H:i:s'),
            'level' =&gt; $level,
            'message' =&gt; $message,
            'context' =&gt; $context
        ];

        foreach ($this-&gt;handlers as $handler) {
            $handler-&gt;handle($entry);
        }
    }
}</code></pre>
<h3>Security</h3>
<pre><code class="language-php">// Security helper
class Security {
    public static function sanitizeInput($input) {
        if (is_array($input)) {
            return array_map([self::class, 'sanitizeInput'], $input);
        }
        return htmlspecialchars($input, ENT_QUOTES, 'UTF-8');
    }

    public static function validateToken($token) {
        try {
            $decoded = JWT::decode($token, getenv('JWT_SECRET'), ['HS256']);
            return $decoded-&gt;exp &gt; time();
        } catch (Exception $e) {
            return false;
        }
    }

    public static function generateCSRF() {
        if (empty($_SESSION['csrf_token'])) {
            $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
        }
        return $_SESSION['csrf_token'];
    }
}</code></pre>
<h3>Performance</h3>
<pre><code class="language-php">// Cache helper
class Cache {
    private $redis;

    public function __construct() {
        $this-&gt;redis = new Redis();
        $this-&gt;redis-&gt;connect('127.0.0.1', 6379);
    }

    public function remember($key, $ttl, callable $callback) {
        if ($value = $this-&gt;redis-&gt;get($key)) {
            return unserialize($value);
        }

        $value = $callback();
        $this-&gt;redis-&gt;setex($key, $ttl, serialize($value));
        return $value;
    }

    public function flush($pattern = '*') {
        $keys = $this-&gt;redis-&gt;keys($pattern);
        foreach ($keys as $key) {
            $this-&gt;redis-&gt;del($key);
        }
    }
}</code></pre>
<h2>2. Ricerca Dati Catastali</h2>
<h3>Inizializzazione Layer WMS</h3>
<pre><code class="language-javascript">// Configurazione layer catastale
const wmsUrl = 'https://wms.cartografia.agenziaentrate.gov.it/inspire/wms/ows01.php';
const wmsLayer = L.tileLayer.wms(wmsUrl, {
    layers: 'CP.CadastralParcel',
    styles: 'default',
    format: 'image/png',
    transparent: true,
    version: '1.3.0',
    crs: L.CRS.EPSG4326,
    minZoom: 16,
    maxZoom: 20
});

// Aggiunta layer alla mappa
map.addLayer(wmsLayer);</code></pre>
<h3>Ricerca per Click</h3>
<pre><code class="language-javascript">// Gestione click sulla mappa
map.on('click', async e =&gt; {
    const { lat, lng } = e.latlng;

    try {
        // Richiesta dati catastali
        const data = await getCadastralInfo(lat, lng);

        // Aggiornamento form
        document.getElementById('catasto_comune').value = data.comune;
        document.getElementById('catasto_foglio').value = data.foglio;
        document.getElementById('catasto_particella').value = data.particella;

        // Log risultato
        console.log('Dati catastali:', data);
    } catch (error) {
        console.error('Errore recupero dati catastali:', error);
    }
});</code></pre>
<h3>Funzione Recupero Dati</h3>
<pre><code class="language-javascript">// Recupero dati catastali
async function getCadastralInfo(lat, lng) {
    // Verifica zoom minimo
    if (map.getZoom() &lt; 16) {
        throw new Error('Zoom insufficiente per dati catastali');
    }

    // Chiamata API
    const response = await fetch('/api/cadastral_data.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({ lat, lng })
    });

    // Verifica risposta
    if (!response.ok) {
        throw new Error('Errore recupero dati catastali');
    }

    return await response.json();
}</code></pre>
<h3>Gestione Errori</h3>
<pre><code class="language-javascript">// Gestione errori catastali
function handleCadastralError(error) {
    // Log errore
    console.error('Errore catastale:', error);

    // Messaggio utente
    const message = error.message || 'Errore recupero dati catastali';
    alert(message);

    // Reset form
    document.getElementById('catasto_comune').value = '';
    document.getElementById('catasto_foglio').value = '';
    document.getElementById('catasto_particella').value = '';
}</code></pre>
<h1>Flussi di Lavoro</h1>
<h2>Dashboard Amministrativa</h2>
<h3>Monitoraggio Sistema</h3>
<ol>
<li>
<p><strong>Controllo Performance</strong></p>
<ul>
<li>Verifica metriche sistema ogni 30 minuti</li>
<li>Analisi log errori</li>
<li>Monitoraggio spazio disco</li>
<li>Controllo stato servizi</li>
</ul>
</li>
<li>
<p><strong>Gestione Backup</strong></p>
<ul>
<li>Backup automatico giornaliero (03:00)</li>
<li>Verifica integrità backup</li>
<li>Pulizia backup obsoleti</li>
<li>Notifica risultati backup</li>
</ul>
</li>
<li>
<p><strong>Sicurezza</strong></p>
<ul>
<li>Monitoraggio accessi sospetti</li>
<li>Verifica tentativi login falliti</li>
<li>Controllo permessi file</li>
<li>Scan vulnerabilità</li>
</ul>
</li>
<li>
<p><strong>Manutenzione</strong></p>
<ul>
<li>Ottimizzazione database settimanale</li>
<li>Pulizia file temporanei</li>
<li>Aggiornamento cache</li>
<li>Verifica integrità dati</li>
</ul>
</li>
</ol>
<h3>Procedure di Emergenza</h3>
<ol>
<li>
<p><strong>Problemi Performance</strong></p>
<ul>
<li>Analisi log errori</li>
<li>Restart servizi critici</li>
<li>Pulizia cache</li>
<li>Notifica amministratori</li>
</ul>
</li>
<li>
<p><strong>Violazioni Sicurezza</strong></p>
<ul>
<li>Blocco IP sospetti</li>
<li>Reset password compromesse</li>
<li>Backup dati critici</li>
<li>Report dettagliato
<pre><code></code></pre></li>
</ul>
</li>
</ol>

        </div>
    </div>

    <button class="print-button" id="printBtn" title="Stampa documento">
        <i class="fas fa-print"></i>
    </button>

    <script>
    $(document).ready(function() {
        // Configurazione Mermaid
        mermaid.initialize({
            startOnLoad: false,
            theme: 'default',
            securityLevel: 'loose',
            flowchart: {
                curve: 'basis',
                padding: 15,
                useMaxWidth: true,
                htmlLabels: true,
                nodeSpacing: 50,
                rankSpacing: 50
            }
        });

        // Inizializza Mermaid dopo il caricamento della pagina
        setTimeout(function() {
            mermaid.run({
                querySelector: '.mermaid'
            });
        }, 500);
        
        // Rimuovi eventuali pulsanti di chiusura esistenti
        $('.close-button').remove();
        
        // Aggiungi pulsante chiudi dinamicamente solo se non esiste già
        if ($('#closeBtn').length === 0) {
            $('body').append(
                $('<button>')
                    .attr('id', 'closeBtn')
                    .addClass('close-button')
                    .attr('title', 'Chiudi')
                    .html('<i class="fas fa-times"></i>')
                    .on('click', function() {
                        // Verifica se siamo in un iframe
                        if (window !== window.top) {
                            // Accedi direttamente al popup nel parent e chiudilo
                            const frameElement = window.frameElement;
                            if (frameElement) {
                                const popup = frameElement.closest('.doc-popup');
                                if (popup) {
                                    popup.style.display = 'none';
                                }
                            }
                        } else {
                            // Altrimenti chiudi la finestra
                            window.close();
                        }
                    })
            );
        }

        // Gestione link interni
        $('a[href^="#"]').on('click', function(e) {
            e.preventDefault();
            
            const target = $($(this).attr('href'));
            if (target.length) {
                $('html, body').animate({
                    scrollTop: target.offset().top - 20
                }, 500);
                
                // Aggiorna URL senza ricaricare la pagina
                history.pushState(null, null, $(this).attr('href'));
                
                // Evidenzia la sezione attiva
                $('.doc-section').removeClass('active');
                target.addClass('active');
                
                // Evidenzia il link nell'indice
                $('.toc a').removeClass('active');
                $('.toc a[href="' + $(this).attr('href') + '"]').addClass('active');
            }
        });
        
        // Genera indice laterale
        const toc = $('#toc');
        $('.doc-section').each(function() {
            const section = $(this);
            const id = section.attr('id');
            const title = section.find('h3').first().text();
            
            toc.append(
                $('<li>').append(
                    $('<a>')
                        .attr('href', '#' + id)
                        .text(title)
                )
            );
        });
        
        // Evidenzia sezione corrente durante lo scroll
        $(window).scroll(function() {
            const scrollPos = $(window).scrollTop();
            
            $('.doc-section').each(function() {
                const section = $(this);
                const sectionTop = section.offset().top - 100;
                const sectionBottom = sectionTop + section.outerHeight();
                
                if (scrollPos >= sectionTop && scrollPos < sectionBottom) {
                    $('.doc-section').removeClass('active');
                    section.addClass('active');
                    
                    $('.toc a').removeClass('active');
                    $('.toc a[href="#' + section.attr('id') + '"]').addClass('active');
                    
                    // Aggiorna URL senza ricaricare la pagina
                    history.replaceState(null, null, '#' + section.attr('id'));
                }
            });
        });
        
        // Gestione pulsante stampa
        $('#printBtn').on('click', function() {
            window.print();
        });
    });
    </script>
</body>
</html>