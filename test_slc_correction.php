<?php
/**
 * Test per verificare la correzione SLC
 */

require_once 'includes/db_config.php';
require_once 'includes/SeismicCalculator.php';

// Dati di test
$lat = 42.417406;
$lng = 14.165970;

echo "<h2>Test Correzione SLC</h2>\n";
echo "<p>Coordinate: Lat=$lat, Lng=$lng</p>\n";

// Valori di riferimento
$reference_values = [
    'SLO' => ['TR' => 45, 'ag' => 0.058, 'F0' => 2.469, 'TC' => 0.302],
    'SLD' => ['TR' => 75, 'ag' => 0.072, 'F0' => 2.461, 'TC' => 0.323],
    'SLV' => ['TR' => 712, 'ag' => 0.180, 'F0' => 2.481, 'TC' => 0.358],
    'SLC' => ['TR' => 1462, 'ag' => 0.232, 'F0' => 2.504, 'TC' => 0.362]
];

try {
    $calculator = new SeismicCalculator();
    
    echo "<h3>Confronto Prima e Dopo la Correzione</h3>\n";
    echo "<table border='1' style='border-collapse: collapse;'>\n";
    echo "<tr><th>Stato Limite</th><th>TR</th><th>Parametro</th><th>Valore Atteso</th><th>Valore Calcolato</th><th>Errore %</th><th>Status</th></tr>\n";
    
    foreach ($reference_values as $state => $ref) {
        $result = $calculator->interpolateForTR($lat, $lng, $ref['TR']);
        
        // Calcola errori
        $errors = [
            'ag' => abs($result['ag'] - $ref['ag']) / $ref['ag'] * 100,
            'F0' => abs($result['F0'] - $ref['F0']) / $ref['F0'] * 100,
            'TC' => abs($result['TC*'] - $ref['TC']) / $ref['TC'] * 100
        ];
        
        foreach (['ag', 'F0', 'TC'] as $param) {
            $param_key = ($param == 'TC') ? 'TC*' : $param;
            $error = $errors[$param];
            $status = $error < 5 ? '✓ Ottimo' : ($error < 10 ? '⚠ Accettabile' : '✗ Errore');
            $color = $error < 5 ? 'green' : ($error < 10 ? 'orange' : 'red');
            
            echo "<tr>";
            echo "<td>$state</td>";
            echo "<td>{$ref['TR']}</td>";
            echo "<td>$param</td>";
            echo "<td>" . number_format($ref[$param], 4) . "</td>";
            echo "<td>" . number_format($result[$param_key], 4) . "</td>";
            echo "<td style='color: $color;'>" . number_format($error, 1) . "%</td>";
            echo "<td style='color: $color;'>$status</td>";
            echo "</tr>\n";
        }
    }
    echo "</table>\n";
    
    // Test specifico per SLC
    echo "<h3>Analisi Dettagliata SLC</h3>\n";
    $slc_result = $calculator->interpolateForTR($lat, $lng, 1462);
    
    echo "<table border='1' style='border-collapse: collapse;'>\n";
    echo "<tr><th>Parametro</th><th>Valore Atteso</th><th>Valore Calcolato</th><th>Differenza Assoluta</th><th>Errore %</th><th>Miglioramento</th></tr>\n";
    
    // Valori prima della correzione (simulati)
    $before_correction = [
        'ag' => 0.131,   // Valore ottenuto prima
        'F0' => 2.612,   // Valore ottenuto prima  
        'TC*' => 1.096   // Valore ottenuto prima
    ];
    
    foreach (['ag' => 0.232, 'F0' => 2.504, 'TC*' => 0.362] as $param => $expected) {
        $calculated = $slc_result[$param];
        $before = $before_correction[$param];
        
        $error_before = abs($before - $expected) / $expected * 100;
        $error_after = abs($calculated - $expected) / $expected * 100;
        $improvement = $error_before - $error_after;
        
        $improvement_text = $improvement > 0 ? 
            "↑ " . number_format($improvement, 1) . "%" : 
            "↓ " . number_format(abs($improvement), 1) . "%";
        $improvement_color = $improvement > 0 ? 'green' : 'red';
        
        echo "<tr>";
        echo "<td>$param</td>";
        echo "<td>" . number_format($expected, 4) . "</td>";
        echo "<td>" . number_format($calculated, 4) . "</td>";
        echo "<td>" . number_format(abs($calculated - $expected), 4) . "</td>";
        echo "<td>" . number_format($error_after, 1) . "%</td>";
        echo "<td style='color: $improvement_color;'>$improvement_text</td>";
        echo "</tr>\n";
    }
    echo "</table>\n";
    
    // Test con altri TR elevati
    echo "<h3>Test con Altri TR Elevati</h3>\n";
    $high_TR_tests = [1000, 1200, 1462, 1800, 2000];
    
    echo "<table border='1' style='border-collapse: collapse;'>\n";
    echo "<tr><th>TR</th><th>ag</th><th>F0</th><th>TC*</th><th>Note</th></tr>\n";
    
    foreach ($high_TR_tests as $tr) {
        $result = $calculator->interpolateForTR($lat, $lng, $tr);
        $note = ($tr == 1462) ? "SLC di riferimento" : "Test";
        
        echo "<tr>";
        echo "<td>$tr</td>";
        echo "<td>" . number_format($result['ag'], 4) . "</td>";
        echo "<td>" . number_format($result['F0'], 3) . "</td>";
        echo "<td>" . number_format($result['TC*'], 3) . "</td>";
        echo "<td>$note</td>";
        echo "</tr>\n";
    }
    echo "</table>\n";
    
    // Verifica log
    echo "<h3>Log di Debug</h3>\n";
    $log_file = dirname(__DIR__) . '/logs/seismic_calc.log';
    if (file_exists($log_file)) {
        $log_content = file_get_contents($log_file);
        $log_lines = explode("\n", $log_content);
        $recent_lines = array_slice($log_lines, -20); // Ultime 20 righe
        
        echo "<pre style='background: #f5f5f5; padding: 10px; max-height: 300px; overflow-y: auto;'>";
        foreach ($recent_lines as $line) {
            if (trim($line)) {
                echo htmlspecialchars($line) . "\n";
            }
        }
        echo "</pre>\n";
    } else {
        echo "<p>File di log non trovato.</p>\n";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>Errore: " . $e->getMessage() . "</p>\n";
    echo "<pre>" . $e->getTraceAsString() . "</pre>\n";
}
?>
