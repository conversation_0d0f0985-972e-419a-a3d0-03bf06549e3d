<?php
/**
 * Script di verifica completa del database dopo l'importazione
 * Verifica stato database e test calcoli sismici
 */

require_once 'includes/db_config.php';
require_once 'includes/SeismicCalculator.php';

echo "=== VERIFICA COMPLETA DATABASE FINALE ===\n\n";

try {
    // Connessione database
    echo "1. Connessione database...\n";
    $conn = new PDO("mysql:host=localhost;dbname=asdp_db;charset=utf8", "root", "");
    $conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "   ✓ Connesso al database\n\n";
    
    // Verifica stato generale database
    echo "2. Verifica stato generale database...\n";
    
    $stmt = $conn->query("SELECT COUNT(*) as total FROM seismic_grid_points");
    $total_points = $stmt->fetch(PDO::FETCH_ASSOC)['total'];
    echo "   Punti totali nel database: $total_points\n";
    
    // Verifica TC_975 (dovre<PERSON> essere tutto corretto ora)
    echo "\n3. Verifica TC_975...\n";
    $stmt = $conn->query("
        SELECT 
            COUNT(*) as total,
            COUNT(CASE WHEN tc_975 = 0 THEN 1 END) as zero_count,
            COUNT(CASE WHEN tc_975 > 0 THEN 1 END) as valid_count,
            MIN(tc_975) as min_val,
            MAX(tc_975) as max_val,
            AVG(tc_975) as avg_val
        FROM seismic_grid_points
    ");
    
    $stats975 = $stmt->fetch(PDO::FETCH_ASSOC);
    echo "   TC_975 - Totali: {$stats975['total']}\n";
    echo "   TC_975 - Zero: {$stats975['zero_count']}\n";
    echo "   TC_975 - Validi: {$stats975['valid_count']}\n";
    echo "   TC_975 - Range: " . number_format($stats975['min_val'], 4) . " - " . number_format($stats975['max_val'], 4) . "\n";
    echo "   TC_975 - Media: " . number_format($stats975['avg_val'], 4) . "\n";
    
    if ($stats975['zero_count'] == 0) {
        echo "   ✓ TC_975: TUTTI I VALORI CORRETTI (nessun zero)\n";
    } else {
        echo "   ✗ TC_975: Ancora {$stats975['zero_count']} valori zero\n";
    }
    
    // Verifica TC_2475 (dovrebbe essere tutto corretto ora)
    echo "\n4. Verifica TC_2475...\n";
    $stmt = $conn->query("
        SELECT 
            COUNT(*) as total,
            COUNT(CASE WHEN tc_2475 = 0 THEN 1 END) as zero_count,
            COUNT(CASE WHEN tc_2475 > 1.0 THEN 1 END) as anomalous_count,
            COUNT(CASE WHEN tc_2475 <= 1.0 AND tc_2475 > 0 THEN 1 END) as normal_count,
            MIN(tc_2475) as min_val,
            MAX(tc_2475) as max_val,
            AVG(tc_2475) as avg_val
        FROM seismic_grid_points
    ");
    
    $stats2475 = $stmt->fetch(PDO::FETCH_ASSOC);
    echo "   TC_2475 - Totali: {$stats2475['total']}\n";
    echo "   TC_2475 - Zero: {$stats2475['zero_count']}\n";
    echo "   TC_2475 - Normali (≤1.0): {$stats2475['normal_count']}\n";
    echo "   TC_2475 - Anomali (>1.0): {$stats2475['anomalous_count']}\n";
    echo "   TC_2475 - Range: " . number_format($stats2475['min_val'], 4) . " - " . number_format($stats2475['max_val'], 4) . "\n";
    echo "   TC_2475 - Media: " . number_format($stats2475['avg_val'], 4) . "\n";
    
    if ($stats2475['anomalous_count'] == 0) {
        echo "   ✓ TC_2475: TUTTI I VALORI NORMALI (≤1.0)\n";
    } else {
        echo "   ⚠ TC_2475: Ancora {$stats2475['anomalous_count']} valori anomali (>1.0)\n";
    }
    
    // Verifica copertura dati per tutti i TR
    echo "\n5. Verifica copertura dati per tutti i TR...\n";
    $tr_list = [30, 50, 72, 101, 140, 201, 475, 975, 2475];
    
    foreach ($tr_list as $tr) {
        $stmt = $conn->query("
            SELECT 
                COUNT(CASE WHEN ag_$tr > 0 THEN 1 END) as ag_valid,
                COUNT(CASE WHEN fo_$tr > 0 THEN 1 END) as fo_valid,
                COUNT(CASE WHEN tc_$tr > 0 THEN 1 END) as tc_valid,
                AVG(ag_$tr) as ag_avg,
                AVG(fo_$tr) as fo_avg,
                AVG(tc_$tr) as tc_avg
            FROM seismic_grid_points
        ");
        
        $tr_stats = $stmt->fetch(PDO::FETCH_ASSOC);
        $coverage = min($tr_stats['ag_valid'], $tr_stats['fo_valid'], $tr_stats['tc_valid']);
        $percentage = round(($coverage / $total_points) * 100, 1);
        
        echo "   TR $tr: $coverage/$total_points punti ($percentage%) - ";
        echo "AG:" . number_format($tr_stats['ag_avg'], 2) . " ";
        echo "FO:" . number_format($tr_stats['fo_avg'], 2) . " ";
        echo "TC:" . number_format($tr_stats['tc_avg'], 3) . "\n";
    }
    
    // Test coordinate di riferimento specifiche
    echo "\n6. Test coordinate di riferimento...\n";
    $test_coordinates = [
        ['lat' => 42.417406, 'lng' => 14.165970, 'name' => 'Coordinate Test Principali'],
        ['lat' => 41.9028, 'lng' => 12.4964, 'name' => 'Roma'],
        ['lat' => 45.4642, 'lng' => 9.1900, 'name' => 'Milano'],
        ['lat' => 40.8518, 'lng' => 14.2681, 'name' => 'Napoli']
    ];
    
    $calculator = new SeismicCalculator();
    
    foreach ($test_coordinates as $coord) {
        echo "\n   {$coord['name']} (Lat: {$coord['lat']}, Lng: {$coord['lng']}):\n";
        
        // Verifica se esistono dati per queste coordinate
        $stmt = $conn->prepare("
            SELECT id, latitude, longitude, tc_975, tc_2475,
                   ABS(latitude - ?) + ABS(longitude - ?) as distance
            FROM seismic_grid_points 
            ORDER BY distance 
            LIMIT 1
        ");
        $stmt->execute([$coord['lat'], $coord['lng']]);
        $nearest = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($nearest) {
            echo "     Punto più vicino: ID {$nearest['id']} (distanza: " . number_format($nearest['distance'], 6) . ")\n";
            echo "     TC_975: " . number_format($nearest['tc_975'], 4) . ", TC_2475: " . number_format($nearest['tc_2475'], 4) . "\n";
            
            if ($nearest['distance'] < 0.01) {
                echo "     ✓ Coordinate coperte dal database\n";
            } else {
                echo "     ⚠ Coordinate non precisamente coperte\n";
            }
        }
    }
    
    // Test calcoli sismici completi
    echo "\n7. Test calcoli sismici completi...\n";
    
    $lat = 42.417406;
    $lng = 14.165970;
    
    $reference_values = [
        'SLO' => ['TR' => 45, 'ag' => 0.058, 'F0' => 2.469, 'TC' => 0.302],
        'SLD' => ['TR' => 75, 'ag' => 0.072, 'F0' => 2.461, 'TC' => 0.323],
        'SLV' => ['TR' => 712, 'ag' => 0.180, 'F0' => 2.481, 'TC' => 0.358],
        'SLC' => ['TR' => 1462, 'ag' => 0.232, 'F0' => 2.504, 'TC' => 0.362]
    ];
    
    $all_passed = true;
    $results_summary = [];
    
    foreach ($reference_values as $state => $ref) {
        echo "\n   $state (TR={$ref['TR']}):\n";
        $result = $calculator->interpolateForTR($lat, $lng, $ref['TR']);
        
        $state_results = [];
        foreach (['ag', 'F0', 'TC'] as $param) {
            $param_key = ($param == 'TC') ? 'TC*' : $param;
            $expected = $ref[$param];
            $calculated = $result[$param_key];
            $error = abs($calculated - $expected) / $expected * 100;
            
            $status = $error < 10 ? "✓" : "✗";
            if ($error >= 10) $all_passed = false;
            
            echo "     $param: " . number_format($calculated, 4) . " (atteso: " . number_format($expected, 4) . ", errore: " . number_format($error, 1) . "%) $status\n";
            
            $state_results[$param] = [
                'calculated' => $calculated,
                'expected' => $expected,
                'error' => $error,
                'passed' => $error < 10
            ];
        }
        $results_summary[$state] = $state_results;
    }
    
    // Riepilogo finale
    echo "\n8. Riepilogo finale...\n";
    echo str_repeat("=", 80) . "\n";
    
    if ($stats975['zero_count'] == 0) {
        echo "   ✓ TC_975: PROBLEMA RISOLTO\n";
    } else {
        echo "   ✗ TC_975: Problema persistente\n";
    }
    
    if ($stats2475['anomalous_count'] == 0) {
        echo "   ✓ TC_2475: PROBLEMA RISOLTO\n";
    } else {
        echo "   ⚠ TC_2475: Problema parzialmente risolto\n";
    }
    
    if ($all_passed) {
        echo "   ✓ CALCOLI SLC: TUTTI I TEST SUPERATI!\n";
        echo "   🎉 PROBLEMA SLC RISOLTO DEFINITIVAMENTE!\n";
    } else {
        echo "   ✗ CALCOLI SLC: Alcuni test non superati\n";
        
        // Dettagli errori
        foreach ($results_summary as $state => $state_results) {
            $failed_params = array_filter($state_results, function($r) { return !$r['passed']; });
            if (!empty($failed_params)) {
                echo "     $state: " . implode(', ', array_keys($failed_params)) . " ancora con errori\n";
            }
        }
    }
    
    echo "\n   Database totale: $total_points punti\n";
    echo "   Importazione: " . ($all_passed ? "SUCCESSO COMPLETO" : "PARZIALE") . "\n";
    
    if ($all_passed) {
        echo "\n🎉 CONGRATULAZIONI! Il database è stato aggiornato con successo!\n";
        echo "✅ Tutti i problemi sismici sono stati risolti!\n";
        echo "✅ L'applicazione ASDP è ora completamente funzionante!\n";
    }
    
    echo "\n=== VERIFICA COMPLETATA ===\n";
    
} catch (Exception $e) {
    echo "ERRORE: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}
?>
