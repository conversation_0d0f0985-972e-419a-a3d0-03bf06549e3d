<?php
/**
 * Test per verificare i dati INGV e confrontarli con i valori di riferimento
 * Questo test aiuta a determinare se il problema è nei nostri calcoli o nei dati di riferimento
 */

echo "<h2>Verifica Dati INGV vs Riferimenti</h2>\n";

// Coordinate di test
$lat = 42.417406;
$lng = 14.165970;

echo "<p><strong>Coordinate:</strong> Lat=$lat, Lng=$lng</p>\n";
echo "<p><strong>Località:</strong> Via Aterno, 140, 66020 Zona Industriale</p>\n";

// Valori di riferimento forniti
$reference_values = [
    'SLO' => ['TR' => 45, 'ag' => 0.058, 'F0' => 2.469, 'TC' => 0.302],
    'SLD' => ['TR' => 75, 'ag' => 0.072, 'F0' => 2.461, 'TC' => 0.323],
    'SLV' => ['TR' => 712, 'ag' => 0.180, 'F0' => 2.481, 'TC' => 0.358],
    'SLC' => ['TR' => 1462, 'ag' => 0.232, 'F0' => 2.504, 'TC' => 0.362]
];

try {
    $conn = new PDO("mysql:host=localhost;dbname=asdp_db;charset=utf8", "root", "");
    $conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // 1. Verifica i dati grezzi nel database per i TR di riferimento
    echo "<h3>1. Dati Grezzi nel Database</h3>\n";
    
    $stmt = $conn->prepare("
        SELECT 
            latitude, longitude,
            ag_30/9.81 as ag_30, fo_30, tc_30,
            ag_50/9.81 as ag_50, fo_50, tc_50,
            ag_72/9.81 as ag_72, fo_72, tc_72,
            ag_101/9.81 as ag_101, fo_101, tc_101,
            ag_140/9.81 as ag_140, fo_140, tc_140,
            ag_201/9.81 as ag_201, fo_201, tc_201,
            ag_475/9.81 as ag_475, fo_475, tc_475,
            ag_975/9.81 as ag_975, fo_975, tc_975,
            ag_2475/9.81 as ag_2475, fo_2475, tc_2475,
            POW(latitude - ?, 2) + POW(longitude - ?, 2) as distance
        FROM seismic_grid_points
        WHERE latitude BETWEEN ? - 0.1 AND ? + 0.1 
        AND longitude BETWEEN ? - 0.1 AND ? + 0.1 
        ORDER BY POW(latitude - ?, 2) + POW(longitude - ?, 2)
        LIMIT 4
    ");
    
    $stmt->execute([$lat, $lng, $lat, $lat, $lng, $lng, $lat, $lng]);
    $points = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<p><strong>Punti più vicini nel database:</strong></p>\n";
    foreach ($points as $i => $point) {
        $distance_km = sqrt($point['distance']) * 111;
        echo "<h4>Punto " . ($i+1) . " - Lat: " . number_format($point['latitude'], 6) . 
             ", Lng: " . number_format($point['longitude'], 6) . 
             " (Distanza: " . number_format($distance_km, 1) . " km)</h4>\n";
        
        echo "<table border='1' style='border-collapse: collapse; font-size: 12px;'>\n";
        echo "<tr><th>TR</th><th>ag (g)</th><th>F0</th><th>Tc*</th></tr>\n";
        
        $TR_values = [30, 50, 72, 101, 140, 201, 475, 975, 2475];
        foreach ($TR_values as $tr) {
            echo "<tr>";
            echo "<td>$tr</td>";
            echo "<td>" . number_format($point["ag_$tr"], 4) . "</td>";
            echo "<td>" . number_format($point["fo_$tr"], 3) . "</td>";
            echo "<td>" . number_format($point["tc_$tr"], 3) . "</td>";
            echo "</tr>\n";
        }
        echo "</table>\n";
    }
    
    // 2. Calcola interpolazione manuale per i TR di riferimento
    echo "<h3>2. Interpolazione Manuale per TR di Riferimento</h3>\n";
    
    // Ordina i punti per interpolazione
    usort($points, function($a, $b) {
        if ($a['latitude'] != $b['latitude']) {
            return $a['latitude'] < $b['latitude'] ? -1 : 1;
        }
        return $a['longitude'] < $b['longitude'] ? -1 : 1;
    });
    
    // Coefficienti di interpolazione spaziale
    $x = ($lng - $points[0]['longitude']) / ($points[1]['longitude'] - $points[0]['longitude']);
    $y = ($lat - $points[0]['latitude']) / ($points[2]['latitude'] - $points[0]['latitude']);
    
    echo "<p>Coefficienti interpolazione spaziale: x=" . number_format($x, 6) . ", y=" . number_format($y, 6) . "</p>\n";
    
    echo "<table border='1' style='border-collapse: collapse;'>\n";
    echo "<tr><th>Stato Limite</th><th>TR Target</th><th>TR Lower</th><th>TR Upper</th><th>Parametro</th><th>Valore Calcolato</th><th>Valore Atteso</th><th>Errore %</th></tr>\n";
    
    foreach ($reference_values as $state => $ref) {
        $TR_target = $ref['TR'];
        
        // Trova TR lower e upper
        $TR_values = [30, 50, 72, 101, 140, 201, 475, 975, 2475];
        $lower_TR = null;
        $upper_TR = null;
        
        for ($i = 0; $i < count($TR_values); $i++) {
            if ($TR_values[$i] > $TR_target) {
                if ($i > 0) {
                    $lower_TR = $TR_values[$i-1];
                    $upper_TR = $TR_values[$i];
                } else {
                    $lower_TR = $TR_values[0];
                    $upper_TR = $TR_values[1];
                }
                break;
            }
        }
        
        if ($lower_TR === null) {
            $lower_TR = $TR_values[count($TR_values)-2];
            $upper_TR = $TR_values[count($TR_values)-1];
        }
        
        // Interpolazione spaziale per lower TR
        $ag_lower = $points[0]["ag_$lower_TR"] * (1-$x) * (1-$y) +
                    $points[1]["ag_$lower_TR"] * $x * (1-$y) +
                    $points[2]["ag_$lower_TR"] * (1-$x) * $y +
                    $points[3]["ag_$lower_TR"] * $x * $y;
        
        $fo_lower = $points[0]["fo_$lower_TR"] * (1-$x) * (1-$y) +
                    $points[1]["fo_$lower_TR"] * $x * (1-$y) +
                    $points[2]["fo_$lower_TR"] * (1-$x) * $y +
                    $points[3]["fo_$lower_TR"] * $x * $y;
        
        $tc_lower = $points[0]["tc_$lower_TR"] * (1-$x) * (1-$y) +
                    $points[1]["tc_$lower_TR"] * $x * (1-$y) +
                    $points[2]["tc_$lower_TR"] * (1-$x) * $y +
                    $points[3]["tc_$lower_TR"] * $x * $y;
        
        // Interpolazione spaziale per upper TR
        $ag_upper = $points[0]["ag_$upper_TR"] * (1-$x) * (1-$y) +
                    $points[1]["ag_$upper_TR"] * $x * (1-$y) +
                    $points[2]["ag_$upper_TR"] * (1-$x) * $y +
                    $points[3]["ag_$upper_TR"] * $x * $y;
        
        $fo_upper = $points[0]["fo_$upper_TR"] * (1-$x) * (1-$y) +
                    $points[1]["fo_$upper_TR"] * $x * (1-$y) +
                    $points[2]["fo_$upper_TR"] * (1-$x) * $y +
                    $points[3]["fo_$upper_TR"] * $x * $y;
        
        $tc_upper = $points[0]["tc_$upper_TR"] * (1-$x) * (1-$y) +
                    $points[1]["tc_$upper_TR"] * $x * (1-$y) +
                    $points[2]["tc_$upper_TR"] * (1-$x) * $y +
                    $points[3]["tc_$upper_TR"] * $x * $y;
        
        // Interpolazione temporale
        $x_time = (log($TR_target) - log($lower_TR)) / (log($upper_TR) - log($lower_TR));
        
        $ag_final = $ag_lower + $x_time * ($ag_upper - $ag_lower);
        $fo_final = $fo_lower + $x_time * ($fo_upper - $fo_lower);
        $tc_final = $tc_lower + $x_time * ($tc_upper - $tc_lower);
        
        // Calcola errori
        $error_ag = abs($ag_final - $ref['ag']) / $ref['ag'] * 100;
        $error_fo = abs($fo_final - $ref['F0']) / $ref['F0'] * 100;
        $error_tc = abs($tc_final - $ref['TC']) / $ref['TC'] * 100;
        
        // Mostra risultati
        foreach (['ag' => [$ag_final, $ref['ag'], $error_ag], 
                  'F0' => [$fo_final, $ref['F0'], $error_fo], 
                  'TC' => [$tc_final, $ref['TC'], $error_tc]] as $param => $data) {
            
            $color = $data[2] < 5 ? 'green' : ($data[2] < 15 ? 'orange' : 'red');
            
            echo "<tr>";
            echo "<td>$state</td>";
            echo "<td>$TR_target</td>";
            echo "<td>$lower_TR</td>";
            echo "<td>$upper_TR</td>";
            echo "<td>$param</td>";
            echo "<td>" . number_format($data[0], 4) . "</td>";
            echo "<td>" . number_format($data[1], 4) . "</td>";
            echo "<td style='color: $color;'>" . number_format($data[2], 1) . "%</td>";
            echo "</tr>\n";
        }
    }
    echo "</table>\n";
    
    // 3. Analisi delle discrepanze
    echo "<h3>3. Analisi delle Discrepanze</h3>\n";
    echo "<p>Basandomi sui risultati sopra, possiamo determinare se:</p>\n";
    echo "<ul>\n";
    echo "<li><strong>I dati di riferimento sono corretti</strong> e il nostro metodo di interpolazione ha problemi</li>\n";
    echo "<li><strong>I dati di riferimento sono per coordinate/parametri diversi</strong> rispetto a quelli nel nostro database</li>\n";
    echo "<li><strong>Il database contiene dati di una versione diversa</strong> rispetto ai riferimenti</li>\n";
    echo "</ul>\n";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>Errore: " . $e->getMessage() . "</p>\n";
}
?>
