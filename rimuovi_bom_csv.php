<?php
/**
 * Script per rimuovere BOM (Byte Order Mark) dal file CSV
 * Risolve il problema di chiave duplicata causato dal BOM UTF-8
 */

echo "=== RIMOZIONE BOM DA CSV FINALE ===\n\n";

$input_file = 'docs/analisi_normative/conversione_finale.CSV';
$output_file = 'docs/analisi_normative/conversione_clean.CSV';

try {
    // Verifica esistenza file
    if (!file_exists($input_file)) {
        die("ERRORE: File $input_file non trovato\n");
    }
    
    echo "1. Analisi file CSV...\n";
    echo "   Input: $input_file\n";
    echo "   Output: $output_file\n";
    echo "   Dimensione input: " . number_format(filesize($input_file)) . " bytes\n";
    
    // Leggi tutto il contenuto del file
    $content = file_get_contents($input_file);
    if ($content === false) {
        die("ERRORE: Impossibile leggere il file\n");
    }
    
    echo "   Contenuto letto: " . number_format(strlen($content)) . " caratteri\n";
    
    // Controlla presenza BOM
    $has_bom = false;
    if (substr($content, 0, 3) === "\xEF\xBB\xBF") {
        $has_bom = true;
        echo "   ✓ BOM UTF-8 rilevato all'inizio del file\n";
    } else {
        echo "   ⚠ Nessun BOM UTF-8 rilevato\n";
    }
    
    // Rimuovi BOM se presente
    if ($has_bom) {
        $content = substr($content, 3);
        echo "   ✓ BOM rimosso (3 bytes)\n";
    }
    
    // Analizza prime righe per altri problemi
    echo "\n2. Analisi prime righe...\n";
    $lines = explode("\n", $content);
    
    for ($i = 0; $i < min(5, count($lines)); $i++) {
        $line = trim($lines[$i]);
        if (empty($line)) continue;
        
        $parts = explode(';', $line);
        $id = trim($parts[0]);
        
        // Controlla caratteri invisibili nell'ID
        $clean_id = preg_replace('/[^\x20-\x7E]/', '', $id);
        $has_invisible = ($id !== $clean_id);
        
        echo "   Riga " . ($i + 1) . ":\n";
        echo "     ID originale: '$id' (lunghezza: " . strlen($id) . ")\n";
        echo "     ID pulito: '$clean_id' (lunghezza: " . strlen($clean_id) . ")\n";
        echo "     Caratteri invisibili: " . ($has_invisible ? "SÌ" : "NO") . "\n";
        
        if ($has_invisible) {
            // Mostra i byte dell'ID per debug
            $hex = '';
            for ($j = 0; $j < strlen($id); $j++) {
                $hex .= sprintf('%02X ', ord($id[$j]));
            }
            echo "     Hex dump ID: $hex\n";
        }
        echo "\n";
    }
    
    // Pulizia aggressiva: rimuovi tutti i caratteri invisibili dagli ID
    echo "3. Pulizia caratteri invisibili...\n";
    
    $lines_cleaned = [];
    $total_lines = 0;
    $cleaned_lines = 0;
    
    foreach ($lines as $line) {
        $total_lines++;
        
        if (empty(trim($line))) {
            continue; // Salta righe vuote
        }
        
        $parts = explode(';', $line);
        if (count($parts) < 30) {
            continue; // Salta righe incomplete
        }
        
        // Pulisci l'ID (prima colonna)
        $original_id = $parts[0];
        $clean_id = preg_replace('/[^\x20-\x7E]/', '', trim($original_id));
        
        if ($original_id !== $clean_id) {
            $cleaned_lines++;
        }
        
        $parts[0] = $clean_id;
        $lines_cleaned[] = implode(';', $parts);
    }
    
    echo "   Righe totali: $total_lines\n";
    echo "   Righe con ID puliti: $cleaned_lines\n";
    echo "   Righe valide finali: " . count($lines_cleaned) . "\n";
    
    // Ricostruisci il contenuto
    $clean_content = implode("\n", $lines_cleaned);
    
    // Scrivi file pulito
    echo "\n4. Scrittura file pulito...\n";
    
    $result = file_put_contents($output_file, $clean_content);
    if ($result === false) {
        die("ERRORE: Impossibile scrivere il file di output\n");
    }
    
    echo "   ✓ File scritto: $output_file\n";
    echo "   Dimensione output: " . number_format(filesize($output_file)) . " bytes\n";
    
    // Verifica finale
    echo "\n5. Verifica file pulito...\n";
    
    $handle = fopen($output_file, 'r');
    if ($handle) {
        $verify_count = 0;
        $verify_ids = [];
        $duplicates = 0;
        
        while (($row = fgetcsv($handle, 0, ';')) !== FALSE) {
            $verify_count++;
            
            if (count($row) < 30) continue;
            
            $id = trim($row[0]);
            
            // Controlla duplicati
            if (isset($verify_ids[$id])) {
                $duplicates++;
                echo "   ⚠ Duplicato trovato: ID '$id' (riga $verify_count)\n";
            } else {
                $verify_ids[$id] = $verify_count;
            }
            
            // Mostra prime 3 righe per verifica
            if ($verify_count <= 3) {
                echo "   Riga $verify_count: ID='$id', colonne=" . count($row) . "\n";
            }
        }
        
        fclose($handle);
        
        echo "\n   Righe nel file finale: $verify_count\n";
        echo "   ID unici: " . count($verify_ids) . "\n";
        echo "   Duplicati: $duplicates\n";
        
        if ($duplicates == 0) {
            echo "   ✅ NESSUN DUPLICATO!\n";
        } else {
            echo "   ❌ Ancora $duplicates duplicati\n";
        }
        
        // Verifica ID 10007
        if (isset($verify_ids['10007'])) {
            echo "   ✅ ID 10007 presente\n";
        } else {
            echo "   ⚠ ID 10007 mancante\n";
        }
    }
    
    echo "\n6. Riepilogo...\n";
    echo "   " . str_repeat("=", 50) . "\n";
    
    if ($has_bom) {
        echo "   ✅ BOM UTF-8 RIMOSSO\n";
    }
    
    if ($cleaned_lines > 0) {
        echo "   ✅ CARATTERI INVISIBILI RIMOSSI: $cleaned_lines righe\n";
    }
    
    echo "   ✅ FILE PULITO: $output_file\n";
    echo "   ✅ PRONTO PER IMPORTAZIONE\n";
    
    echo "\n=== PULIZIA BOM COMPLETATA ===\n";
    echo "Usa il file: $output_file per l'importazione\n";
    
} catch (Exception $e) {
    echo "ERRORE: " . $e->getMessage() . "\n";
}
?>
