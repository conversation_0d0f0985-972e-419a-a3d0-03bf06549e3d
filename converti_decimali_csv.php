<?php
/**
 * Script per convertire decimali da formato italiano (virgola) a inglese (punto)
 * Risolve il problema "Data truncated" di MySQL
 */

echo "=== CONVERSIONE DECIMALI CSV (VIRGOLA → PUNTO) ===\n\n";

$input_file = 'docs/analisi_normative/conversione_clean.CSV';
$output_file = 'docs/analisi_normative/conversione_mysql.CSV';

try {
    // Verifica esistenza file
    if (!file_exists($input_file)) {
        die("ERRORE: File $input_file non trovato\n");
    }
    
    echo "1. Apertura file CSV...\n";
    echo "   Input: $input_file\n";
    echo "   Output: $output_file\n";
    echo "   Dimensione input: " . number_format(filesize($input_file)) . " bytes\n\n";
    
    // Apri file di input
    $handle_input = fopen($input_file, 'r');
    if (!$handle_input) {
        die("ERRORE: Impossibile aprire il file di input\n");
    }
    
    // Crea file di output
    $handle_output = fopen($output_file, 'w');
    if (!$handle_output) {
        fclose($handle_input);
        die("ERRORE: Impossibile creare il file di output\n");
    }
    
    echo "2. Conversione decimali...\n";
    
    $total_rows = 0;
    $converted_values = 0;
    $sample_conversions = [];
    
    while (($row = fgetcsv($handle_input, 0, ';')) !== FALSE) {
        $total_rows++;
        
        if (count($row) < 30) {
            // Riga incompleta, salta
            continue;
        }
        
        // Converti tutti i valori numerici (tranne l'ID)
        for ($i = 1; $i < count($row); $i++) {
            $original_value = trim($row[$i]);
            
            // Controlla se contiene virgola decimale
            if (strpos($original_value, ',') !== false) {
                $converted_value = str_replace(',', '.', $original_value);
                $row[$i] = $converted_value;
                $converted_values++;
                
                // Salva esempi per verifica (primi 10)
                if (count($sample_conversions) < 10) {
                    $sample_conversions[] = [
                        'row' => $total_rows,
                        'column' => $i,
                        'original' => $original_value,
                        'converted' => $converted_value
                    ];
                }
            }
        }
        
        // Scrivi riga convertita
        fputcsv($handle_output, $row, ';');
        
        if ($total_rows % 1000 == 0) {
            echo "   Processate $total_rows righe, conversioni: $converted_values...\n";
        }
    }
    
    fclose($handle_input);
    fclose($handle_output);
    
    echo "\n3. Risultati conversione...\n";
    echo "   Righe totali processate: $total_rows\n";
    echo "   Valori convertiti (virgola → punto): $converted_values\n";
    echo "   File output: $output_file\n";
    echo "   Dimensione output: " . number_format(filesize($output_file)) . " bytes\n\n";
    
    // Mostra esempi di conversioni
    if (!empty($sample_conversions)) {
        echo "4. Esempi di conversioni...\n";
        foreach ($sample_conversions as $conv) {
            echo "   Riga {$conv['row']}, colonna {$conv['column']}: '{$conv['original']}' → '{$conv['converted']}'\n";
        }
        echo "\n";
    }
    
    // Verifica file convertito
    echo "5. Verifica file convertito...\n";
    
    $handle_verify = fopen($output_file, 'r');
    if ($handle_verify) {
        $verify_count = 0;
        $decimal_points = 0;
        $decimal_commas = 0;
        
        // Verifica prime 5 righe
        for ($i = 0; $i < 5; $i++) {
            $row = fgetcsv($handle_verify, 0, ';');
            if (!$row) break;
            
            $verify_count++;
            
            echo "   Riga $verify_count:\n";
            echo "     ID: {$row[0]}\n";
            echo "     LON: {$row[1]} (era con virgola)\n";
            echo "     LAT: {$row[2]} (era con virgola)\n";
            echo "     AG_30: {$row[3]} (era con virgola)\n";
            echo "     FO_30: {$row[4]} (era con virgola)\n";
            echo "     TC_30: {$row[5]} (era con virgola)\n";
            
            // Conta punti e virgole decimali
            for ($j = 1; $j < count($row); $j++) {
                if (strpos($row[$j], '.') !== false) {
                    $decimal_points++;
                }
                if (strpos($row[$j], ',') !== false) {
                    $decimal_commas++;
                }
            }
            echo "\n";
        }
        
        fclose($handle_verify);
        
        echo "   Valori con punto decimale: $decimal_points\n";
        echo "   Valori con virgola decimale: $decimal_commas\n";
        
        if ($decimal_commas == 0) {
            echo "   ✅ NESSUNA VIRGOLA DECIMALE rimanente!\n";
        } else {
            echo "   ⚠ Ancora $decimal_commas virgole decimali\n";
        }
    }
    
    // Test di validazione MySQL
    echo "\n6. Test validazione MySQL...\n";
    
    $handle_test = fopen($output_file, 'r');
    if ($handle_test) {
        $test_row = fgetcsv($handle_test, 0, ';');
        if ($test_row && count($test_row) >= 30) {
            echo "   Test conversione valori:\n";
            
            // Test coordinate
            $lng = floatval($test_row[1]);
            $lat = floatval($test_row[2]);
            echo "     LON: '{$test_row[1]}' → $lng\n";
            echo "     LAT: '{$test_row[2]}' → $lat\n";
            
            // Test valori sismici
            $ag_30 = floatval($test_row[3]);
            $fo_30 = floatval($test_row[4]);
            $tc_30 = floatval($test_row[5]);
            echo "     AG_30: '{$test_row[3]}' → $ag_30\n";
            echo "     FO_30: '{$test_row[4]}' → $fo_30\n";
            echo "     TC_30: '{$test_row[5]}' → $tc_30\n";
            
            // Verifica validità
            $valid_coords = ($lng > 0 && $lat > 0);
            $valid_seismic = ($ag_30 > 0 && $fo_30 > 0 && $tc_30 > 0);
            
            echo "     Coordinate valide: " . ($valid_coords ? "✅" : "❌") . "\n";
            echo "     Valori sismici validi: " . ($valid_seismic ? "✅" : "❌") . "\n";
        }
        
        fclose($handle_test);
    }
    
    echo "\n7. Riepilogo...\n";
    echo "   " . str_repeat("=", 50) . "\n";
    
    if ($converted_values > 0) {
        echo "   ✅ DECIMALI CONVERTITI: $converted_values valori\n";
        echo "   ✅ FORMATO MYSQL: Virgole → Punti\n";
        echo "   ✅ FILE PRONTO: $output_file\n";
    } else {
        echo "   ⚠ NESSUNA CONVERSIONE necessaria\n";
    }
    
    echo "\n=== CONVERSIONE DECIMALI COMPLETATA ===\n";
    echo "Usa il file: $output_file per l'importazione MySQL\n";
    
    // Comando SQL suggerito
    echo "\nComando SQL per importazione:\n";
    echo "LOAD DATA INFILE 'C:/xampp/htdocs/progetti/asdp/docs/analisi_normative/conversione_mysql.CSV'\n";
    echo "INTO TABLE seismic_grid_points\n";
    echo "FIELDS TERMINATED BY ';'\n";
    echo "LINES TERMINATED BY '\\n'\n";
    echo "IGNORE 0 LINES;\n";
    
} catch (Exception $e) {
    echo "ERRORE: " . $e->getMessage() . "\n";
    if (isset($handle_input)) fclose($handle_input);
    if (isset($handle_output)) fclose($handle_output);
}
?>
