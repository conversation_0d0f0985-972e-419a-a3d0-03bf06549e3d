<?php
/**
 * Script intelligente per importare tutti i dati dal file Excel
 * Strategia: analizza la struttura e importa tutti i fogli sistematicamente
 */

require_once 'includes/db_config.php';
require_once 'includes/SimpleXLSX.php';

use <PERSON><PERSON><PERSON>\SimpleXLSX;

echo "=== IMPORTAZIONE INTELLIGENTE DATI EXCEL ===\n\n";

$excel_file = 'docs/analisi_normative/10408-pdf16.xlsx';

try {
    // Apri il file Excel
    echo "1. Apertura file Excel...\n";
    $xlsx = SimpleXLSX::parse($excel_file);
    
    if (!$xlsx) {
        die("ERRORE: " . SimpleXLSX::parseError() . "\n");
    }
    
    $sheet_names = $xlsx->sheetNames();
    echo "   ✓ File aperto: " . count($sheet_names) . " fogli\n\n";
    
    // Connessione database
    echo "2. Connessione database...\n";
    $conn = new PDO("mysql:host=localhost;dbname=asdp_db;charset=utf8", "root", "");
    $conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "   ✓ Connesso al database\n\n";
    
    // Backup completo
    echo "3. Creazione backup completo...\n";
    $backup_table = "seismic_grid_points_backup_smart_" . date('Y_m_d_H_i_s');
    $conn->exec("CREATE TABLE $backup_table AS SELECT * FROM seismic_grid_points");
    echo "   ✓ Backup creato: $backup_table\n\n";
    
    // Analizza la struttura dei primi fogli per capire il pattern
    echo "4. Analisi struttura dati...\n";
    
    $data_structure = [];
    $tr_mapping = [];
    
    // Analizza i primi 20 fogli per identificare il pattern
    for ($i = 0; $i < min(20, count($sheet_names)); $i++) {
        echo "   Analizzando foglio $i: {$sheet_names[$i]}...\n";
        
        $rows = $xlsx->rows($i);
        if (!$rows) {
            echo "     ✗ Errore lettura foglio\n";
            continue;
        }
        
        $row_count = 0;
        $sample_data = [];
        $has_coordinates = false;
        $has_seismic_data = false;
        
        foreach ($rows as $row) {
            $row_count++;
            if ($row_count > 100) break; // Limita per performance
            
            if ($row_count <= 10) {
                $sample_data[] = $row;
            }
            
            // Cerca coordinate italiane e dati sismici
            if (count($row) >= 5) {
                for ($j = 0; $j < count($row); $j++) {
                    $val = floatval($row[$j]);
                    
                    // Coordinate Italia
                    if ($val >= 35 && $val <= 48) {
                        $has_coordinates = true;
                    }
                    
                    // Valori sismici tipici
                    if ($val > 0 && $val < 10 && $j > 2) {
                        $has_seismic_data = true;
                    }
                }
            }
        }
        
        $data_structure[$i] = [
            'name' => $sheet_names[$i],
            'rows' => $row_count,
            'has_coordinates' => $has_coordinates,
            'has_seismic_data' => $has_seismic_data,
            'sample' => $sample_data
        ];
        
        echo "     Righe: $row_count, Coordinate: " . ($has_coordinates ? "Sì" : "No") . 
             ", Dati sismici: " . ($has_seismic_data ? "Sì" : "No") . "\n";
        
        // Se il foglio ha dati validi, prova a mapparlo a un TR
        if ($has_coordinates && $has_seismic_data) {
            // I primi 9 fogli dovrebbero corrispondere ai 9 TR
            $tr_values = [30, 50, 72, 101, 140, 201, 475, 975, 2475];
            if ($i < count($tr_values)) {
                $tr_mapping[$i] = $tr_values[$i];
                echo "     → Mappato a TR={$tr_values[$i]}\n";
            }
        }
    }
    
    echo "\n5. Importazione dati identificati...\n";
    
    $total_updated = 0;
    
    foreach ($tr_mapping as $sheet_index => $tr) {
        echo "   Importazione TR=$tr (Foglio {$sheet_names[$sheet_index]})...\n";
        
        $rows = $xlsx->rows($sheet_index);
        if (!$rows) {
            echo "     ✗ Errore lettura foglio\n";
            continue;
        }
        
        $row_count = 0;
        $updated_count = 0;
        $header_found = false;
        
        // Analizza la struttura del foglio per trovare le colonne
        $lat_col = -1;
        $lng_col = -1;
        $ag_col = -1;
        $fo_col = -1;
        $tc_col = -1;
        
        // Prepara statement di update
        $stmt = $conn->prepare("
            UPDATE seismic_grid_points 
            SET ag_$tr = ?, fo_$tr = ?, tc_$tr = ?
            WHERE ABS(latitude - ?) < 0.001 AND ABS(longitude - ?) < 0.001
        ");
        
        foreach ($rows as $row) {
            $row_count++;
            
            // Analizza le prime righe per identificare la struttura
            if (!$header_found && $row_count <= 20) {
                
                // Metodo 1: Cerca header testuale
                for ($i = 0; $i < count($row); $i++) {
                    $cell = strtolower(trim($row[$i]));
                    if (strpos($cell, 'lat') !== false && $lat_col == -1) {
                        $lat_col = $i;
                    } elseif (strpos($cell, 'lon') !== false && $lng_col == -1) {
                        $lng_col = $i;
                    } elseif (strpos($cell, 'ag') !== false && $ag_col == -1) {
                        $ag_col = $i;
                    } elseif (strpos($cell, 'fo') !== false && $fo_col == -1) {
                        $fo_col = $i;
                    } elseif (strpos($cell, 'tc') !== false && $tc_col == -1) {
                        $tc_col = $i;
                    }
                }
                
                // Metodo 2: Analisi automatica dei valori
                if ($lat_col == -1 || $lng_col == -1) {
                    for ($i = 0; $i < min(10, count($row)); $i++) {
                        $val = floatval($row[$i]);
                        
                        // Latitudine Italia: 35-48
                        if ($val >= 35 && $val <= 48 && $lat_col == -1) {
                            $lat_col = $i;
                        }
                        // Longitudine Italia: 6-19
                        elseif ($val >= 6 && $val <= 19 && $lng_col == -1) {
                            $lng_col = $i;
                        }
                    }
                }
                
                // Se troviamo lat/lng, assumiamo che i dati sismici siano nelle ultime colonne
                if ($lat_col >= 0 && $lng_col >= 0 && $ag_col == -1) {
                    $total_cols = count($row);
                    if ($total_cols >= 5) {
                        // Cerca le colonne con valori sismici ragionevoli
                        for ($i = max($lat_col, $lng_col) + 1; $i < $total_cols; $i++) {
                            $val = floatval($row[$i]);
                            if ($val > 0 && $val < 50) { // Range ragionevole per dati sismici
                                if ($ag_col == -1) $ag_col = $i;
                                elseif ($fo_col == -1) $fo_col = $i;
                                elseif ($tc_col == -1) $tc_col = $i;
                            }
                        }
                    }
                }
                
                if ($lat_col >= 0 && $lng_col >= 0 && $ag_col >= 0) {
                    $header_found = true;
                    echo "     Struttura identificata alla riga $row_count: lat=$lat_col, lng=$lng_col, ag=$ag_col, fo=$fo_col, tc=$tc_col\n";
                }
            }
            
            if (!$header_found) {
                continue;
            }
            
            // Verifica che la riga abbia abbastanza colonne
            $max_col = max($lat_col, $lng_col, $ag_col, $fo_col, $tc_col);
            if (count($row) <= $max_col || $max_col < 0) {
                continue;
            }
            
            // Estrai i valori
            $lat = floatval($row[$lat_col]);
            $lng = floatval($row[$lng_col]);
            $ag = floatval($row[$ag_col]);
            $fo = $fo_col >= 0 ? floatval($row[$fo_col]) : 2.5; // Valore di default
            $tc = $tc_col >= 0 ? floatval($row[$tc_col]) : 0.35; // Valore di default
            
            // Verifica validità coordinate Italia
            if ($lat < 35 || $lat > 48 || $lng < 6 || $lng > 19) {
                continue;
            }
            
            // Verifica validità dati sismici
            if ($ag <= 0) {
                continue;
            }
            
            // Converti ag da g a m/s² se necessario
            if ($ag < 1.0) {
                $ag = $ag * 9.81;
            }
            
            // Aggiorna il database
            $stmt->execute([$ag, $fo, $tc, $lat, $lng]);
            $updated_count += $stmt->rowCount();
            
            if ($row_count % 1000 == 0) {
                echo "     Processate $row_count righe, aggiornate $updated_count...\n";
            }
        }
        
        echo "     ✓ TR=$tr: $row_count righe processate, $updated_count punti aggiornati\n";
        $total_updated += $updated_count;
    }
    
    echo "\n6. Risultati finali...\n";
    echo "   Punti totali aggiornati: $total_updated\n";
    
    // Verifica finale
    require_once 'includes/SeismicCalculator.php';
    $calculator = new SeismicCalculator();
    
    $lat = 42.417406;
    $lng = 14.165970;
    
    $reference_values = [
        'SLC' => ['TR' => 1462, 'ag' => 0.232, 'F0' => 2.504, 'TC' => 0.362]
    ];
    
    echo "\n7. Test finale...\n";
    foreach ($reference_values as $state => $ref) {
        $result = $calculator->interpolateForTR($lat, $lng, $ref['TR']);
        
        foreach (['ag', 'F0', 'TC'] as $param) {
            $param_key = ($param == 'TC') ? 'TC*' : $param;
            $expected = $ref[$param];
            $calculated = $result[$param_key];
            $error = abs($calculated - $expected) / $expected * 100;
            
            $status = $error < 10 ? "✓" : "✗";
            echo "   $state $param: " . number_format($calculated, 4) . " (atteso: " . number_format($expected, 4) . ", errore: " . number_format($error, 1) . "%) $status\n";
        }
    }
    
    echo "\n=== IMPORTAZIONE INTELLIGENTE COMPLETATA ===\n";
    
} catch (Exception $e) {
    echo "ERRORE: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}
?>
