<?php
/**
 * Script per importare i dati direttamente dal file Excel
 * File: docs/analisi_normative/10408-pdf16.xlsx
 */

require_once 'includes/db_config.php';
require_once 'includes/SimpleXLSX.php';

use <PERSON><PERSON><PERSON>\SimpleXLSX;

echo "=== IMPORTAZIONE DIRETTA DA EXCEL ===\n\n";

// Verifica se il file Excel esiste
$excel_file = 'docs/analisi_normative/10408-pdf16.xlsx';
if (!file_exists($excel_file)) {
    die("ERRORE: File Excel non trovato: $excel_file\n");
}

echo "File Excel: $excel_file\n";
echo "Dimensione: " . number_format(filesize($excel_file)) . " bytes\n\n";

try {
    // Apri il file Excel
    echo "1. Apertura file Excel...\n";
    $xlsx = SimpleXLSX::parse($excel_file);
    
    if (!$xlsx) {
        die("ERRORE: Impossibile aprire il file Excel: " . SimpleXLSX::parseError() . "\n");
    }
    
    echo "   ✓ File Excel aperto con successo\n";
    
    // Mostra i fogli disponibili
    $sheet_names = $xlsx->sheetNames();
    echo "   Fogli disponibili (" . count($sheet_names) . "):\n";
    foreach ($sheet_names as $i => $name) {
        echo "     Foglio $i: $name\n";
    }
    echo "\n";
    
    // Connessione database
    echo "2. Connessione database...\n";
    $conn = new PDO("mysql:host=localhost;dbname=asdp_db;charset=utf8", "root", "");
    $conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "   ✓ Connesso al database\n\n";
    
    // Backup completo
    echo "3. Creazione backup completo...\n";
    $backup_table = "seismic_grid_points_backup_excel_import_" . date('Y_m_d_H_i_s');
    $conn->exec("CREATE TABLE $backup_table AS SELECT * FROM seismic_grid_points");
    echo "   ✓ Backup creato: $backup_table\n\n";
    
    // Mappa dei fogli ai periodi di ritorno
    // Assumiamo che i fogli siano ordinati per TR
    $tr_values = [30, 50, 72, 101, 140, 201, 475, 975, 2475];
    
    echo "4. Importazione dati per ogni TR...\n";
    
    foreach ($tr_values as $sheet_index => $tr) {
        if ($sheet_index >= count($sheet_names)) {
            echo "   ⚠ Foglio per TR=$tr non trovato (indice $sheet_index)\n";
            continue;
        }
        
        echo "   Importazione TR=$tr (Foglio: {$sheet_names[$sheet_index]})...\n";
        
        // Leggi i dati del foglio
        $rows = $xlsx->rows($sheet_index);
        if (!$rows) {
            echo "     ✗ Errore lettura foglio\n";
            continue;
        }
        
        $row_count = 0;
        $updated_count = 0;
        $header_found = false;
        $lat_col = -1;
        $lng_col = -1;
        $ag_col = -1;
        $fo_col = -1;
        $tc_col = -1;
        
        // Prepara statement di update
        $stmt = $conn->prepare("
            UPDATE seismic_grid_points 
            SET ag_$tr = ?, fo_$tr = ?, tc_$tr = ?
            WHERE ABS(latitude - ?) < 0.001 AND ABS(longitude - ?) < 0.001
        ");
        
        foreach ($rows as $row) {
            $row_count++;
            
            // Cerca l'header nelle prime 10 righe
            if (!$header_found && $row_count <= 10) {
                for ($i = 0; $i < count($row); $i++) {
                    $cell = strtolower(trim($row[$i]));
                    if (strpos($cell, 'lat') !== false || strpos($cell, 'latitude') !== false) {
                        $lat_col = $i;
                    } elseif (strpos($cell, 'lon') !== false || strpos($cell, 'longitude') !== false) {
                        $lng_col = $i;
                    } elseif (strpos($cell, 'ag') !== false) {
                        $ag_col = $i;
                    } elseif (strpos($cell, 'fo') !== false || strpos($cell, 'f0') !== false) {
                        $fo_col = $i;
                    } elseif (strpos($cell, 'tc') !== false) {
                        $tc_col = $i;
                    }
                }
                
                if ($lat_col >= 0 && $lng_col >= 0 && $ag_col >= 0 && $fo_col >= 0 && $tc_col >= 0) {
                    $header_found = true;
                    echo "     Header trovato alla riga $row_count: lat=$lat_col, lng=$lng_col, ag=$ag_col, fo=$fo_col, tc=$tc_col\n";
                    continue;
                }
            }
            
            // Se non abbiamo trovato l'header, assumiamo un formato standard
            if (!$header_found && $row_count > 10) {
                $lat_col = 0;
                $lng_col = 1;
                $ag_col = 2;
                $fo_col = 3;
                $tc_col = 4;
                $header_found = true;
                echo "     Usando formato standard: lat=0, lng=1, ag=2, fo=3, tc=4\n";
            }
            
            if (!$header_found) {
                continue;
            }
            
            // Verifica che la riga abbia abbastanza colonne
            if (count($row) <= max($lat_col, $lng_col, $ag_col, $fo_col, $tc_col)) {
                continue;
            }
            
            // Estrai i valori
            $lat = floatval($row[$lat_col]);
            $lng = floatval($row[$lng_col]);
            $ag = floatval($row[$ag_col]);
            $fo = floatval($row[$fo_col]);
            $tc = floatval($row[$tc_col]);
            
            // Verifica validità dei dati
            if ($lat == 0 || $lng == 0 || ($ag == 0 && $fo == 0 && $tc == 0)) {
                continue;
            }
            
            // Converti ag da g a m/s² se necessario
            if ($ag < 1.0) {
                $ag = $ag * 9.81;
            }
            
            // Aggiorna il database
            $stmt->execute([$ag, $fo, $tc, $lat, $lng]);
            $updated_count += $stmt->rowCount();
            
            if ($row_count % 1000 == 0) {
                echo "     Processate $row_count righe, aggiornate $updated_count...\n";
            }
        }
        
        echo "     ✓ TR=$tr: $row_count righe processate, $updated_count punti aggiornati\n\n";
    }
    
    echo "5. Verifica risultati...\n";
    
    // Verifica tc_975
    $stmt = $conn->query("
        SELECT 
            COUNT(*) as total,
            COUNT(CASE WHEN tc_975 = 0 THEN 1 END) as zero_count,
            MIN(tc_975) as min_val,
            MAX(tc_975) as max_val,
            AVG(tc_975) as avg_val
        FROM seismic_grid_points
    ");
    
    $stats = $stmt->fetch(PDO::FETCH_ASSOC);
    echo "   TC_975 - Totali: {$stats['total']}, Zero: {$stats['zero_count']}, Min: " . number_format($stats['min_val'], 3) . ", Max: " . number_format($stats['max_val'], 3) . ", Media: " . number_format($stats['avg_val'], 3) . "\n";
    
    // Verifica tc_2475
    $stmt = $conn->query("
        SELECT 
            COUNT(CASE WHEN tc_2475 > 1.0 THEN 1 END) as anomalous_count,
            MIN(tc_2475) as min_val,
            MAX(tc_2475) as max_val,
            AVG(tc_2475) as avg_val
        FROM seismic_grid_points
    ");
    
    $stats2475 = $stmt->fetch(PDO::FETCH_ASSOC);
    echo "   TC_2475 - Anomali (>1.0): {$stats2475['anomalous_count']}, Min: " . number_format($stats2475['min_val'], 3) . ", Max: " . number_format($stats2475['max_val'], 3) . ", Media: " . number_format($stats2475['avg_val'], 3) . "\n\n";
    
    // Test con coordinate di riferimento
    echo "6. Test con coordinate di riferimento...\n";
    
    require_once 'includes/SeismicCalculator.php';
    $calculator = new SeismicCalculator();
    
    $lat = 42.417406;
    $lng = 14.165970;
    
    $reference_values = [
        'SLV' => ['TR' => 712, 'ag' => 0.180, 'F0' => 2.481, 'TC' => 0.358],
        'SLC' => ['TR' => 1462, 'ag' => 0.232, 'F0' => 2.504, 'TC' => 0.362]
    ];
    
    foreach ($reference_values as $state => $ref) {
        echo "   $state (TR={$ref['TR']}):\n";
        $result = $calculator->interpolateForTR($lat, $lng, $ref['TR']);
        
        foreach (['ag', 'F0', 'TC'] as $param) {
            $param_key = ($param == 'TC') ? 'TC*' : $param;
            $expected = $ref[$param];
            $calculated = $result[$param_key];
            $error = abs($calculated - $expected) / $expected * 100;
            
            $status = $error < 10 ? "✓" : "✗";
            echo "     $param: " . number_format($calculated, 4) . " (atteso: " . number_format($expected, 4) . ", errore: " . number_format($error, 1) . "%) $status\n";
        }
        echo "\n";
    }
    
    echo "=== IMPORTAZIONE EXCEL COMPLETATA ===\n";
    
} catch (Exception $e) {
    echo "ERRORE: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}
?>
