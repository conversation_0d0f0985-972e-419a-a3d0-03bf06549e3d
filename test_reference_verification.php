<?php
/**
 * Test per verificare i valori di riferimento
 * Confronta con le tabelle ufficiali INGV
 */

echo "<h2>Verifica Valori di Riferimento</h2>\n";

// Coordinate di test
$lat = 42.417406;
$lng = 14.165970;

echo "<p><strong>Coordinate:</strong> Lat=$lat, Lng=$lng</p>\n";
echo "<p><strong>Località:</strong> Via Aterno, 140, 66020 Zona Industriale</p>\n";

// Valori di riferimento forniti
$reference_values = [
    'SLO' => ['TR' => 45, 'ag' => 0.058, 'F0' => 2.469, 'TC' => 0.302],
    'SLD' => ['TR' => 75, 'ag' => 0.072, 'F0' => 2.461, 'TC' => 0.323],
    'SLV' => ['TR' => 712, 'ag' => 0.180, 'F0' => 2.481, 'TC' => 0.358],
    'SLC' => ['TR' => 1462, 'ag' => 0.232, 'F0' => 2.504, 'TC' => 0.362]
];

echo "<h3>Valori di Riferimento Forniti</h3>\n";
echo "<table border='1' style='border-collapse: collapse;'>\n";
echo "<tr><th>Stato Limite</th><th>TR [anni]</th><th>ag [g]</th><th>F0 [-]</th><th>Tc* [s]</th></tr>\n";
foreach ($reference_values as $state => $values) {
    echo "<tr>";
    echo "<td>$state</td>";
    echo "<td>{$values['TR']}</td>";
    echo "<td>{$values['ag']}</td>";
    echo "<td>{$values['F0']}</td>";
    echo "<td>{$values['TC']}</td>";
    echo "</tr>\n";
}
echo "</table>\n";

try {
    require_once 'includes/SeismicCalculator.php';
    $calculator = new SeismicCalculator();
    
    echo "<h3>Valori Calcolati dall'Applicazione</h3>\n";
    echo "<table border='1' style='border-collapse: collapse;'>\n";
    echo "<tr><th>Stato Limite</th><th>TR [anni]</th><th>ag [g]</th><th>F0 [-]</th><th>Tc* [s]</th><th>Errore ag</th><th>Errore F0</th><th>Errore Tc*</th></tr>\n";
    
    foreach ($reference_values as $state => $ref) {
        $result = $calculator->interpolateForTR($lat, $lng, $ref['TR']);
        
        $error_ag = abs($result['ag'] - $ref['ag']) / $ref['ag'] * 100;
        $error_f0 = abs($result['F0'] - $ref['F0']) / $ref['F0'] * 100;
        $error_tc = abs($result['TC*'] - $ref['TC']) / $ref['TC'] * 100;
        
        echo "<tr>";
        echo "<td>$state</td>";
        echo "<td>{$ref['TR']}</td>";
        echo "<td>" . number_format($result['ag'], 4) . "</td>";
        echo "<td>" . number_format($result['F0'], 3) . "</td>";
        echo "<td>" . number_format($result['TC*'], 3) . "</td>";
        echo "<td style='color: " . ($error_ag > 10 ? 'red' : 'green') . ";'>" . number_format($error_ag, 1) . "%</td>";
        echo "<td style='color: " . ($error_f0 > 10 ? 'red' : 'green') . ";'>" . number_format($error_f0, 1) . "%</td>";
        echo "<td style='color: " . ($error_tc > 10 ? 'red' : 'green') . ";'>" . number_format($error_tc, 1) . "%</td>";
        echo "</tr>\n";
    }
    echo "</table>\n";
    
    // Analisi dettagliata per SLC
    echo "<h3>Analisi Dettagliata SLC (TR=1462)</h3>\n";
    
    $conn = new PDO("mysql:host=localhost;dbname=asdp_db;charset=utf8", "root", "");
    $conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // Trova i punti più vicini
    $stmt = $conn->prepare("
        SELECT 
            latitude, longitude,
            ag_975/9.81 as ag_975, fo_975, tc_975,
            ag_2475/9.81 as ag_2475, fo_2475, tc_2475,
            POW(latitude - ?, 2) + POW(longitude - ?, 2) as distance
        FROM seismic_grid_points
        WHERE latitude BETWEEN ? - 0.1 AND ? + 0.1 
        AND longitude BETWEEN ? - 0.1 AND ? + 0.1 
        ORDER BY POW(latitude - ?, 2) + POW(longitude - ?, 2)
        LIMIT 10
    ");
    
    $stmt->execute([$lat, $lng, $lat, $lat, $lng, $lng, $lat, $lng]);
    $points = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<p><strong>Punti più vicini nel database (raggio 0.1°):</strong></p>\n";
    echo "<table border='1' style='border-collapse: collapse;'>\n";
    echo "<tr><th>Punto</th><th>Lat</th><th>Lng</th><th>Distanza</th><th>ag_975</th><th>fo_975</th><th>tc_975</th><th>ag_2475</th><th>fo_2475</th><th>tc_2475</th></tr>\n";
    
    foreach ($points as $i => $point) {
        echo "<tr>";
        echo "<td>" . ($i+1) . "</td>";
        echo "<td>" . number_format($point['latitude'], 6) . "</td>";
        echo "<td>" . number_format($point['longitude'], 6) . "</td>";
        echo "<td>" . number_format(sqrt($point['distance']) * 111, 1) . " km</td>";
        echo "<td>" . number_format($point['ag_975'], 4) . "</td>";
        echo "<td>" . number_format($point['fo_975'], 3) . "</td>";
        echo "<td>" . number_format($point['tc_975'], 3) . "</td>";
        echo "<td>" . number_format($point['ag_2475'], 4) . "</td>";
        echo "<td>" . number_format($point['fo_2475'], 3) . "</td>";
        echo "<td>" . number_format($point['tc_2475'], 3) . "</td>";
        echo "</tr>\n";
    }
    echo "</table>\n";
    
    // Verifica se esiste un punto esatto o molto vicino che dia i valori attesi
    echo "<h3>Ricerca Punto con Valori Simili ai Riferimenti</h3>\n";
    
    // Calcola interpolazione per il punto più vicino
    $closest = $points[0];
    $x_time = (log(1462) - log(975)) / (log(2475) - log(975));
    
    $ag_interp = $closest['ag_975'] + $x_time * ($closest['ag_2475'] - $closest['ag_975']);
    $fo_interp = $closest['fo_975'] + $x_time * ($closest['fo_2475'] - $closest['fo_975']);
    $tc_interp = $closest['tc_975'] + $x_time * ($closest['tc_2475'] - $closest['tc_975']);
    
    echo "<p><strong>Interpolazione per il punto più vicino (senza interpolazione spaziale):</strong></p>\n";
    echo "<table border='1' style='border-collapse: collapse;'>\n";
    echo "<tr><th>Parametro</th><th>Valore Interpolato</th><th>Valore Atteso</th><th>Differenza</th></tr>\n";
    echo "<tr><td>ag</td><td>" . number_format($ag_interp, 4) . "</td><td>0.232</td><td>" . number_format(abs($ag_interp - 0.232), 4) . "</td></tr>\n";
    echo "<tr><td>F0</td><td>" . number_format($fo_interp, 3) . "</td><td>2.504</td><td>" . number_format(abs($fo_interp - 2.504), 3) . "</td></tr>\n";
    echo "<tr><td>Tc*</td><td>" . number_format($tc_interp, 3) . "</td><td>0.362</td><td>" . number_format(abs($tc_interp - 0.362), 3) . "</td></tr>\n";
    echo "</table>\n";
    
    // Verifica se i valori di riferimento potrebbero essere per parametri diversi
    echo "<h3>Possibili Cause delle Differenze</h3>\n";
    echo "<ul>\n";
    echo "<li><strong>Coordinate diverse:</strong> I valori di riferimento potrebbero essere per coordinate leggermente diverse</li>\n";
    echo "<li><strong>Versione database diversa:</strong> I dati nel database potrebbero essere di una versione diversa rispetto ai riferimenti</li>\n";
    echo "<li><strong>Parametri di calcolo diversi:</strong> Vita nominale, classe d'uso, o altri parametri potrebbero essere diversi</li>\n";
    echo "<li><strong>Metodo di interpolazione:</strong> Il metodo di interpolazione potrebbe essere diverso da quello utilizzato per i riferimenti</li>\n";
    echo "</ul>\n";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>Errore: " . $e->getMessage() . "</p>\n";
}
?>
