<?php
/**
 * Script per importare i dati dal file Excel
 * File: docs/analisi_normative/10408-pdf16.xlsx
 */

require_once 'includes/db_config.php';

echo "=== IMPORTAZIONE DATI DA EXCEL ===\n\n";

// Verifica se il file Excel esiste
$excel_file = 'docs/analisi_normative/10408-pdf16.xlsx';
if (!file_exists($excel_file)) {
    die("ERRORE: File Excel non trovato: $excel_file\n");
}

echo "File Excel trovato: $excel_file\n";
echo "Dimensione file: " . number_format(filesize($excel_file)) . " bytes\n\n";

// Verifica se abbiamo SimpleXLSX o altre librerie per leggere Excel
$has_simplexlsx = class_exists('SimpleXLSX');
$has_phpspreadsheet = class_exists('PhpOffice\PhpSpreadsheet\IOFactory');

echo "Librerie disponibili:\n";
echo "- SimpleXLSX: " . ($has_simplexlsx ? "✓ Disponibile" : "✗ Non disponibile") . "\n";
echo "- PhpSpreadsheet: " . ($has_phpspreadsheet ? "✓ Disponibile" : "✗ Non disponibile") . "\n\n";

if (!$has_simplexlsx && !$has_phpspreadsheet) {
    echo "ATTENZIONE: Nessuna libreria Excel trovata.\n";
    echo "Implementeremo un metodo alternativo usando CSV.\n\n";
    
    // Metodo alternativo: chiedi all'utente di convertire Excel in CSV
    echo "ISTRUZIONI:\n";
    echo "1. Apri il file Excel: $excel_file\n";
    echo "2. Salva ogni foglio come file CSV separato nella cartella docs/csv/\n";
    echo "3. Nomina i file come: tc_30.csv, tc_50.csv, tc_72.csv, ecc.\n";
    echo "4. Riavvia questo script\n\n";
    
    // Verifica se esistono file CSV
    $csv_dir = 'docs/csv/';
    if (!is_dir($csv_dir)) {
        mkdir($csv_dir, 0755, true);
        echo "Cartella CSV creata: $csv_dir\n";
    }
    
    $tr_values = [30, 50, 72, 101, 140, 201, 475, 975, 2475];
    $csv_files_exist = [];
    
    foreach ($tr_values as $tr) {
        $csv_file = $csv_dir . "tc_$tr.csv";
        $csv_files_exist[$tr] = file_exists($csv_file);
        echo "- tc_$tr.csv: " . ($csv_files_exist[$tr] ? "✓ Trovato" : "✗ Mancante") . "\n";
    }
    
    $all_csv_exist = !in_array(false, $csv_files_exist);
    
    if ($all_csv_exist) {
        echo "\n✓ Tutti i file CSV sono presenti! Procediamo con l'importazione.\n\n";
        importFromCSV($csv_files_exist, $tr_values);
    } else {
        echo "\n❌ Alcuni file CSV mancano. Segui le istruzioni sopra.\n";
        
        // Crea un file di esempio per mostrare il formato
        $example_csv = $csv_dir . "esempio_formato.csv";
        file_put_contents($example_csv, "latitude,longitude,ag_value,fo_value,tc_value\n42.417406,14.165970,0.232,2.504,0.362\n");
        echo "File di esempio creato: $example_csv\n";
    }
} else {
    echo "Procediamo con la lettura diretta del file Excel.\n\n";
    importFromExcel($excel_file);
}

/**
 * Importa dati dai file CSV
 */
function importFromCSV($csv_files, $tr_values) {
    echo "=== IMPORTAZIONE DA FILE CSV ===\n\n";
    
    try {
        $conn = new PDO("mysql:host=localhost;dbname=asdp_db;charset=utf8", "root", "");
        $conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        // Backup completo
        echo "1. Creazione backup completo...\n";
        $backup_table = "seismic_grid_points_backup_excel_" . date('Y_m_d_H_i_s');
        $conn->exec("CREATE TABLE $backup_table AS SELECT * FROM seismic_grid_points");
        echo "   ✓ Backup creato: $backup_table\n\n";
        
        // Per ogni TR, leggi il CSV corrispondente
        foreach ($tr_values as $tr) {
            $csv_file = "docs/csv/tc_$tr.csv";
            if (!file_exists($csv_file)) {
                echo "   ⚠ File mancante: $csv_file\n";
                continue;
            }
            
            echo "2. Importazione dati per TR=$tr...\n";
            
            $handle = fopen($csv_file, 'r');
            if (!$handle) {
                echo "   ✗ Errore apertura file: $csv_file\n";
                continue;
            }
            
            // Leggi header
            $header = fgetcsv($handle);
            echo "   Header: " . implode(', ', $header) . "\n";
            
            $count = 0;
            $updated = 0;
            
            // Prepara statement di update
            $stmt = $conn->prepare("
                UPDATE seismic_grid_points 
                SET ag_$tr = ?, fo_$tr = ?, tc_$tr = ?
                WHERE ABS(latitude - ?) < 0.001 AND ABS(longitude - ?) < 0.001
            ");
            
            while (($data = fgetcsv($handle)) !== FALSE) {
                $count++;
                
                if (count($data) < 5) {
                    continue; // Salta righe incomplete
                }
                
                $lat = floatval($data[0]);
                $lng = floatval($data[1]);
                $ag = floatval($data[2]);
                $fo = floatval($data[3]);
                $tc = floatval($data[4]);
                
                // Converti ag da g a m/s² se necessario
                if ($ag < 1.0) {
                    $ag = $ag * 9.81;
                }
                
                $stmt->execute([$ag, $fo, $tc, $lat, $lng]);
                $updated += $stmt->rowCount();
                
                if ($count % 1000 == 0) {
                    echo "   Processate $count righe, aggiornate $updated...\n";
                }
            }
            
            fclose($handle);
            echo "   ✓ TR=$tr: $count righe processate, $updated punti aggiornati\n\n";
        }
        
        echo "=== IMPORTAZIONE CSV COMPLETATA ===\n";
        
    } catch (Exception $e) {
        echo "ERRORE: " . $e->getMessage() . "\n";
    }
}

/**
 * Importa dati direttamente dal file Excel
 */
function importFromExcel($excel_file) {
    echo "=== IMPORTAZIONE DA FILE EXCEL ===\n\n";
    
    // Implementazione per lettura diretta Excel
    // Questa funzione sarà implementata se abbiamo le librerie necessarie
    
    echo "Funzione non ancora implementata.\n";
    echo "Usa il metodo CSV per ora.\n";
}

/**
 * Test dei risultati dopo l'importazione
 */
function testResults() {
    echo "=== TEST RISULTATI ===\n\n";
    
    try {
        require_once 'includes/SeismicCalculator.php';
        $calculator = new SeismicCalculator();
        
        $lat = 42.417406;
        $lng = 14.165970;
        
        $reference_values = [
            'SLO' => ['TR' => 45, 'ag' => 0.058, 'F0' => 2.469, 'TC' => 0.302],
            'SLD' => ['TR' => 75, 'ag' => 0.072, 'F0' => 2.461, 'TC' => 0.323],
            'SLV' => ['TR' => 712, 'ag' => 0.180, 'F0' => 2.481, 'TC' => 0.358],
            'SLC' => ['TR' => 1462, 'ag' => 0.232, 'F0' => 2.504, 'TC' => 0.362]
        ];
        
        foreach ($reference_values as $state => $ref) {
            echo "$state (TR={$ref['TR']}):\n";
            $result = $calculator->interpolateForTR($lat, $lng, $ref['TR']);
            
            foreach (['ag', 'F0', 'TC'] as $param) {
                $param_key = ($param == 'TC') ? 'TC*' : $param;
                $expected = $ref[$param];
                $calculated = $result[$param_key];
                $error = abs($calculated - $expected) / $expected * 100;
                
                $status = $error < 10 ? "✓" : "✗";
                echo "  $param: " . number_format($calculated, 4) . " (atteso: " . number_format($expected, 4) . ", errore: " . number_format($error, 1) . "%) $status\n";
            }
            echo "\n";
        }
        
    } catch (Exception $e) {
        echo "ERRORE TEST: " . $e->getMessage() . "\n";
    }
}
?>
